import {
  Box,
  Card,
  Layout,
  List,
  Page,
  Text,
  BlockStack,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Dashboard() {
  return (
    <Page>
      <TitleBar title="Dashboard - AI Assistant Demo" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="300">
              <Text as="h2" variant="headingMd">
                🤖 AI Assistant Dashboard
              </Text>
              <Text as="p" variant="bodyMd">
                Welcome to your AI-powered Shopify assistant! Notice how the chatbot sidebar
                stays fixed on the right side of the screen, even when you navigate between pages.
                This demonstrates the persistent nature of our AI assistant.
              </Text>

              {/* Demo content to show the sidebar doesn't interfere */}
              <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                <BlockStack gap="200">
                  <Text as="h3" variant="headingSm">
                    ✨ AI Assistant Features
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 🔒 Always visible on the right side
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 🔄 Persists across all app pages
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 📱 Collapsible to save screen space
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 🎯 Three user modes: Free, Pro, Developer
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 📎 File attachment support
                  </Text>
                  <Text as="p" variant="bodyMd">
                    • 📱 Responsive design for all screen sizes
                  </Text>
                </BlockStack>
              </Box>

              <Text as="p" variant="bodyMd" tone="subdued">
                Try interacting with the chatbot on the right! It includes mock messages
                and will simulate AI responses when you send a message. Navigate to other
                pages to see how the sidebar persists.
              </Text>
            </BlockStack>
          </Card>
        </Layout.Section>
        <Layout.Section variant="oneThird">
          <Card>
            <BlockStack gap="200">
              <Text as="h2" variant="headingMd">
                🚀 Quick Actions
              </Text>
              <List>
                <List.Item>
                  <Text as="span" variant="bodyMd">
                    Ask the AI: "How can I improve my store?"
                  </Text>
                </List.Item>
                <List.Item>
                  <Text as="span" variant="bodyMd">
                    Try: "Show me my best-selling products"
                  </Text>
                </List.Item>
                <List.Item>
                  <Text as="span" variant="bodyMd">
                    Request: "Help me optimize my homepage"
                  </Text>
                </List.Item>
                <List.Item>
                  <Text as="span" variant="bodyMd">
                    Ask: "What's my conversion rate?"
                  </Text>
                </List.Item>
              </List>

              <Box paddingBlockStart="300">
                <Text as="p" variant="bodySm" tone="subdued">
                  💡 The AI assistant can help with store optimization,
                  product management, analytics insights, and much more!
                </Text>
              </Box>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
