import { useState } from "react";
import {
  Page,
  Layout,
  Card,
  Text,
  Button,
  Badge,
  ProgressBar,
  ResourceList,
  ResourceItem,
  Avatar,
  ButtonGroup,
  InlineStack,
  BlockStack,
  Box,
  Divider,
  Icon,
  Banner,
  DescriptionList,
} from "@shopify/polaris";
import {
  LockIcon,
  ViewIcon,
  CheckIcon,
  ClockIcon,
  StarIcon,
  ProductIcon,
  OrderIcon,
  AppsIcon,
  ThemeIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";

// Mock data for the dashboard - In a real app, this would come from your backend/API
const mockUserPlan: "free" | "pro" | "developer" = "free"; // Can be "free", "pro", or "developer"
const mockMode: "free" | "pro" | "developer" = "developer"; // This would come from user context in real app

// Mock AI suggestions data - These would be generated by your AI backend
const mockAISuggestions = [
  {
    id: "1",
    title: "Add promotional banner to homepage",
    description: "Increase conversions by 15% with a limited-time offer banner highlighting your best-selling products.",
    impact: "High",
    estimatedTime: "5 minutes",
    category: "Marketing",
  },
  {
    id: "2",
    title: "Optimize product descriptions for SEO",
    description: "Improve search rankings by adding relevant keywords to 12 product descriptions that are missing SEO optimization.",
    impact: "Medium",
    estimatedTime: "15 minutes",
    category: "SEO",
  },
  {
    id: "3",
    title: "Create urgency with low stock alerts",
    description: "Add 'Only X left in stock' messages to products with less than 5 items to create purchase urgency.",
    impact: "Medium",
    estimatedTime: "10 minutes",
    category: "Conversion",
  },
];

// Mock recent prompts timeline data - This would come from your chat history
const mockRecentPrompts = [
  {
    id: "1",
    prompt: "How can I improve my product page conversion rate?",
    response: "I analyzed your product pages and found 3 key improvements: add customer reviews, improve product images, and create urgency with stock counters.",
    timestamp: "2 hours ago",
    status: "applied",
    appliedChanges: 2,
  },
  {
    id: "2",
    prompt: "Help me write better product descriptions",
    response: "I've generated SEO-optimized descriptions for your top 10 products, focusing on benefits over features and including relevant keywords.",
    timestamp: "1 day ago",
    status: "previewed",
    appliedChanges: 0,
  },
  {
    id: "3",
    prompt: "What's the best way to organize my product collections?",
    response: "Based on your inventory, I recommend creating 5 main collections: Best Sellers, New Arrivals, Sale Items, Seasonal, and Gift Ideas.",
    timestamp: "3 days ago",
    status: "pending",
    appliedChanges: 0,
  },
];

// Mock store data - This would come from Shopify Admin API
const mockStoreData = {
  productCount: 127,
  ordersThisMonth: 89,
  installedApps: 12,
  themeName: "Dawn",
  revenue: "$12,450",
  conversionRate: "2.4%",
};

// Mock usage data based on plan - This would be tracked in your backend
const mockUsageData = {
  free: { used: 7, total: 10, label: "prompts this month" },
  pro: { used: 45, total: 500, label: "prompts this month" },
  developer: { used: 156, total: -1, label: "unlimited prompts" }, // -1 means unlimited
};

// Mock developer JSON data - For debugging and development mode
const mockDeveloperData = {
  lastResponse: {
    suggestions: 3,
    confidence: 0.92,
    processingTime: "1.2s",
    model: "gpt-4-turbo",
    tokens: { input: 150, output: 300 },
    metadata: {
      storeAnalysis: {
        products: 127,
        categories: 8,
        avgRating: 4.2,
        topCategory: "Electronics"
      }
    }
  }
};

export default function Dashboard() {
  // State for managing which suggestions are being previewed/applied
  // This helps show loading states and prevent multiple clicks
  const [suggestionStates, setSuggestionStates] = useState<{[key: string]: 'idle' | 'previewing' | 'applying'}>({});

  // Get current usage data based on user plan
  const currentUsage = mockUsageData[mockUserPlan as keyof typeof mockUsageData];

  // Calculate usage percentage for progress bar (0-100)
  const usagePercentage = currentUsage.total === -1 ? 0 : (currentUsage.used / currentUsage.total) * 100;

  // Function to handle suggestion preview - shows what changes would look like
  const handlePreview = (suggestionId: string) => {
    setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'previewing' }));
    // Simulate preview loading time
    setTimeout(() => {
      setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'idle' }));
      // In a real app, this would open a preview modal or navigate to preview page
      console.log(`Previewing suggestion ${suggestionId}`);
    }, 2000);
  };

  // Function to handle suggestion apply - actually implements the changes
  // This is locked for free users to encourage upgrades
  const handleApply = (suggestionId: string) => {
    if (mockUserPlan === 'free') {
      // Show upgrade prompt for free users
      alert('Upgrade to Pro to apply AI suggestions automatically!');
      return;
    }

    setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'applying' }));
    // Simulate apply process time
    setTimeout(() => {
      setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'idle' }));
      // In a real app, this would make API calls to implement changes
      console.log(`Applied suggestion ${suggestionId}`);
    }, 3000);
  };

  // Function to get status badge for recent prompts with appropriate colors
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'applied':
        return <Badge tone="success" icon={CheckIcon}>Applied</Badge>;
      case 'previewed':
        return <Badge tone="attention" icon={ViewIcon}>Previewed</Badge>;
      case 'pending':
        return <Badge tone="info" icon={ClockIcon}>Pending</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Function to get plan badge with appropriate styling
  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case 'free':
        return <Badge tone="info">✨ Free</Badge>;
      case 'pro':
        return <Badge tone="attention">⚡ Pro</Badge>;
      case 'developer':
        return <Badge tone="success">🔧 Developer</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page
      title="Dashboard"
      subtitle="Your AI-powered store optimization overview"
      primaryAction={{
        content: "Open AI Assistant",
        onAction: () => {
          // This would open the chatbot sidebar in a real implementation
          console.log("Opening AI Assistant...");
        },
      }}
    >
      <TitleBar title="AI Store Copilot - Dashboard" />

      <Layout>
        {/* 📢 AI Suggestions Section */}
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between" blockAlign="center">
                <Text as="h2" variant="headingMd">
                  🤖 AI Suggestions
                </Text>
                <Badge tone="info">
                  {`${mockAISuggestions.length} suggestions available`}
                </Badge>
              </InlineStack>

              <Text as="p" variant="bodyMd" tone="subdued">
                Smart recommendations from your AI assistant to optimize your store performance.
              </Text>

              <BlockStack gap="300">
                {mockAISuggestions.map((suggestion) => (
                  <Card key={suggestion.id} background="bg-surface-secondary">
                    <BlockStack gap="300">
                      <InlineStack align="space-between" blockAlign="start">
                        <BlockStack gap="100">
                          <Text as="h3" variant="headingSm">
                            {suggestion.title}
                          </Text>
                          <Text as="p" variant="bodyMd">
                            {suggestion.description}
                          </Text>
                        </BlockStack>
                        <Badge tone={suggestion.impact === 'High' ? 'critical' : 'info'}>
                          {`${suggestion.impact} Impact`}
                        </Badge>
                      </InlineStack>

                      <InlineStack gap="200" align="space-between">
                        <InlineStack gap="200">
                          <Text as="span" variant="bodySm" tone="subdued">
                            ⏱️ {suggestion.estimatedTime}
                          </Text>
                          <Text as="span" variant="bodySm" tone="subdued">
                            📂 {suggestion.category}
                          </Text>
                        </InlineStack>

                        <ButtonGroup>
                          <Button
                            icon={ViewIcon}
                            loading={suggestionStates[suggestion.id] === 'previewing'}
                            onClick={() => handlePreview(suggestion.id)}
                          >
                            Preview
                          </Button>
                          <Button
                            variant="primary"
                            icon={mockUserPlan === 'free' ? LockIcon : CheckIcon}
                            loading={suggestionStates[suggestion.id] === 'applying'}
                            onClick={() => handleApply(suggestion.id)}
                          >
                            {mockUserPlan === 'free' ? 'Upgrade to Apply' : 'Apply'}
                          </Button>
                        </ButtonGroup>
                      </InlineStack>
                    </BlockStack>
                  </Card>
                ))}
              </BlockStack>

              {/* Show upgrade banner for free users */}
              {mockUserPlan === 'free' && (
                <Banner
                  title="Unlock AI-Powered Automation"
                  action={{
                    content: 'Upgrade to Pro',
                    onAction: () => console.log('Upgrade clicked'),
                  }}
                  tone="info"
                >
                  <Text as="p" variant="bodyMd">
                    Upgrade to Pro to automatically apply AI suggestions and unlock advanced features.
                  </Text>
                </Banner>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        {/* Two column layout for the rest */}
        <Layout.Section>
          <InlineStack gap="400" align="start">
            {/* Left Column */}
            <Box width="60%">
              <BlockStack gap="400">
                {/* 🕒 Recent Prompts Timeline */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">
                      🕒 Recent Activity
                    </Text>

                    <ResourceList
                      resourceName={{ singular: 'prompt', plural: 'prompts' }}
                      items={mockRecentPrompts}
                      renderItem={(item) => {
                        const { id, prompt, response, timestamp, status, appliedChanges } = item;
                        return (
                          <ResourceItem
                            id={id}
                            accessibilityLabel={`Prompt: ${prompt}`}
                          >
                            <BlockStack gap="200">
                              <InlineStack align="space-between" blockAlign="start">
                                <Text as="h3" variant="bodySm" fontWeight="semibold">
                                  {prompt}
                                </Text>
                                {getStatusBadge(status)}
                              </InlineStack>

                              <Text as="p" variant="bodySm" tone="subdued">
                                {response}
                              </Text>

                              <InlineStack gap="300" align="space-between">
                                <Text as="span" variant="bodyXs" tone="subdued">
                                  {timestamp}
                                </Text>
                                {appliedChanges > 0 && (
                                  <Text as="span" variant="bodyXs" tone="success">
                                    ✅ {appliedChanges} changes applied
                                  </Text>
                                )}
                              </InlineStack>
                            </BlockStack>
                          </ResourceItem>
                        );
                      }}
                    />
                  </BlockStack>
                </Card>

                {/* ⚡ Quick Actions */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">
                      ⚡ Quick Actions
                    </Text>

                    <Text as="p" variant="bodyMd" tone="subdued">
                      Start a conversation with your AI assistant using these common prompts.
                    </Text>

                    <BlockStack gap="200">
                      {[
                        "Improve homepage design",
                        "Optimize product descriptions",
                        "Highlight best sellers",
                        "Analyze conversion funnel",
                        "Review SEO performance",
                        "Create marketing campaign"
                      ].map((action, index) => (
                        <Button
                          key={index}
                          fullWidth
                          textAlign="left"
                          onClick={() => {
                            // In a real app, this would populate the chatbot with the prompt
                            console.log(`Quick action: ${action}`);
                          }}
                        >
                          {action}
                        </Button>
                      ))}
                    </BlockStack>
                  </BlockStack>
                </Card>
              </BlockStack>
            </Box>

            {/* Right Column */}
            <Box width="40%">
              <BlockStack gap="400">
                {/* 📊 Store Snapshot Card */}
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">
                      📊 Store Snapshot
                    </Text>

                    <DescriptionList
                      items={[
                        {
                          term: (
                            <InlineStack gap="100" blockAlign="center">
                              <Icon source={ProductIcon} tone="base" />
                              <Text as="span" variant="bodySm">Products</Text>
                            </InlineStack>
                          ),
                          description: (
                            <Text as="span" variant="headingSm">
                              {mockStoreData.productCount}
                            </Text>
                          ),
                        },
                        {
                          term: (
                            <InlineStack gap="100" blockAlign="center">
                              <Icon source={OrderIcon} tone="base" />
                              <Text as="span" variant="bodySm">Orders this month</Text>
                            </InlineStack>
                          ),
                          description: (
                            <Text as="span" variant="headingSm">
                              {mockStoreData.ordersThisMonth}
                            </Text>
                          ),
                        },
                        {
                          term: (
                            <InlineStack gap="100" blockAlign="center">
                              <Icon source={AppsIcon} tone="base" />
                              <Text as="span" variant="bodySm">Installed apps</Text>
                            </InlineStack>
                          ),
                          description: (
                            <Text as="span" variant="headingSm">
                              {mockStoreData.installedApps}
                            </Text>
                          ),
                        },
                        {
                          term: (
                            <InlineStack gap="100" blockAlign="center">
                              <Icon source={ThemeIcon} tone="base" />
                              <Text as="span" variant="bodySm">Theme</Text>
                            </InlineStack>
                          ),
                          description: (
                            <Text as="span" variant="headingSm">
                              {mockStoreData.themeName}
                            </Text>
                          ),
                        },
                      ]}
                    />

                    <Divider />

                    <BlockStack gap="200">
                      <InlineStack align="space-between">
                        <Text as="span" variant="bodySm">Revenue this month</Text>
                        <Text as="span" variant="headingSm" tone="success">
                          {mockStoreData.revenue}
                        </Text>
                      </InlineStack>
                      <InlineStack align="space-between">
                        <Text as="span" variant="bodySm">Conversion rate</Text>
                        <Text as="span" variant="headingSm">
                          {mockStoreData.conversionRate}
                        </Text>
                      </InlineStack>
                    </BlockStack>
                  </BlockStack>
                </Card>

                {/* 💼 Plan Info Section */}
                <Card>
                  <BlockStack gap="400">
                    <InlineStack align="space-between" blockAlign="center">
                      <Text as="h2" variant="headingMd">
                        💼 Your Plan
                      </Text>
                      {getPlanBadge(mockUserPlan)}
                    </InlineStack>

                    <BlockStack gap="300">
                      <Text as="p" variant="bodyMd">
                        {currentUsage.total === -1
                          ? `You have ${currentUsage.label}`
                          : `${currentUsage.used} of ${currentUsage.total} ${currentUsage.label}`
                        }
                      </Text>

                      {currentUsage.total !== -1 && (
                        <ProgressBar
                          progress={usagePercentage}
                        />
                      )}

                      {mockUserPlan === 'free' && (
                        <BlockStack gap="200">
                          <Text as="p" variant="bodySm" tone="subdued">
                            Upgrade to unlock unlimited prompts, auto-apply suggestions, and advanced features.
                          </Text>
                          <Button variant="primary" fullWidth>
                            Upgrade to Pro
                          </Button>
                        </BlockStack>
                      )}

                      {mockUserPlan === 'pro' && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          You have access to unlimited prompts and auto-apply features.
                        </Text>
                      )}

                      {mockUserPlan === 'developer' && (
                        <Text as="p" variant="bodySm" tone="subdued">
                          You have full access to all features including API access and debug tools.
                        </Text>
                      )}
                    </BlockStack>
                  </BlockStack>
                </Card>

                {/* 🧪 Developer JSON Viewer (only for developer mode) */}
                {mockMode === "developer" && (
                  <Card>
                    <BlockStack gap="400">
                      <Text as="h2" variant="headingMd">
                        🧪 Developer Debug
                      </Text>

                      <Text as="p" variant="bodySm" tone="subdued">
                        Last AI response metadata for debugging:
                      </Text>

                      <Box
                        padding="300"
                        background="bg-surface-secondary"
                        borderRadius="200"
                      >
                        <pre style={{
                          fontSize: '12px',
                          overflow: 'auto',
                          whiteSpace: 'pre-wrap',
                          fontFamily: 'monospace'
                        }}>
                          {JSON.stringify(mockDeveloperData, null, 2)}
                        </pre>
                      </Box>
                    </BlockStack>
                  </Card>
                )}
              </BlockStack>
            </Box>
          </InlineStack>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
