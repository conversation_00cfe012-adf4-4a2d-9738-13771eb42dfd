import { useState } from "react";
import {
  Page,
  Layout,
  Card,
  Text,
  Button,
  Badge,
  ProgressBar,
  InlineStack,
  BlockStack,
  Box,
  Icon,
  Grid,
  CalloutCard,
  ButtonGroup,
} from "@shopify/polaris";
import {
  LockIcon,
  ViewIcon,
  CheckIcon,
  ClockIcon,
  ProductIcon,
  OrderIcon,
  ThemeIcon,
  MagicIcon,
  CashDollarIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";

// Mock data for the dashboard - In a real app, this would come from your backend/API
const mockUserPlan: "free" | "pro" | "developer" = "free"; // Can be "free", "pro", or "developer"
const mockMode: "free" | "pro" | "developer" = "developer"; // This would come from user context in real app

// Mock AI suggestions data - These would be generated by your AI backend
const mockAISuggestions = [
  {
    id: "1",
    title: "Add promotional banner to homepage",
    description: "Increase conversions by 15% with a limited-time offer banner highlighting your best-selling products.",
    impact: "High",
    estimatedTime: "5 minutes",
    category: "Marketing",
  },
  {
    id: "2",
    title: "Optimize product descriptions for SEO",
    description: "Improve search rankings by adding relevant keywords to 12 product descriptions that are missing SEO optimization.",
    impact: "Medium",
    estimatedTime: "15 minutes",
    category: "SEO",
  },
  {
    id: "3",
    title: "Create urgency with low stock alerts",
    description: "Add 'Only X left in stock' messages to products with less than 5 items to create purchase urgency.",
    impact: "Medium",
    estimatedTime: "10 minutes",
    category: "Conversion",
  },
];

// Mock recent prompts timeline data - This would come from your chat history
const mockRecentPrompts = [
  {
    id: "1",
    prompt: "How can I improve my product page conversion rate?",
    response: "I analyzed your product pages and found 3 key improvements: add customer reviews, improve product images, and create urgency with stock counters.",
    timestamp: "2 hours ago",
    status: "applied",
    appliedChanges: 2,
  },
  {
    id: "2",
    prompt: "Help me write better product descriptions",
    response: "I've generated SEO-optimized descriptions for your top 10 products, focusing on benefits over features and including relevant keywords.",
    timestamp: "1 day ago",
    status: "previewed",
    appliedChanges: 0,
  },
  {
    id: "3",
    prompt: "What's the best way to organize my product collections?",
    response: "Based on your inventory, I recommend creating 5 main collections: Best Sellers, New Arrivals, Sale Items, Seasonal, and Gift Ideas.",
    timestamp: "3 days ago",
    status: "pending",
    appliedChanges: 0,
  },
];

// Mock store data - This would come from Shopify Admin API
const mockStoreData = {
  productCount: 127,
  ordersThisMonth: 89,
  installedApps: 12,
  themeName: "Dawn",
  revenue: "$12,450",
  conversionRate: "2.4%",
};

// Mock usage data based on plan - This would be tracked in your backend
const mockUsageData = {
  free: { used: 7, total: 10, label: "prompts this month" },
  pro: { used: 45, total: 500, label: "prompts this month" },
  developer: { used: 156, total: -1, label: "unlimited prompts" }, // -1 means unlimited
};

// Mock developer JSON data - For debugging and development mode
const mockDeveloperData = {
  lastResponse: {
    suggestions: 3,
    confidence: 0.92,
    processingTime: "1.2s",
    model: "gpt-4-turbo",
    tokens: { input: 150, output: 300 },
    metadata: {
      storeAnalysis: {
        products: 127,
        categories: 8,
        avgRating: 4.2,
        topCategory: "Electronics"
      }
    }
  }
};

export default function Dashboard() {
  // State for managing which suggestions are being previewed/applied
  // This helps show loading states and prevent multiple clicks
  const [suggestionStates, setSuggestionStates] = useState<{[key: string]: 'idle' | 'previewing' | 'applying'}>({});

  // Get current usage data based on user plan
  const currentUsage = mockUsageData[mockUserPlan as keyof typeof mockUsageData];

  // Calculate usage percentage for progress bar (0-100)
  const usagePercentage = currentUsage.total === -1 ? 0 : (currentUsage.used / currentUsage.total) * 100;

  // Function to handle suggestion preview - shows what changes would look like
  const handlePreview = (suggestionId: string) => {
    setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'previewing' }));
    // Simulate preview loading time
    setTimeout(() => {
      setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'idle' }));
      // In a real app, this would open a preview modal or navigate to preview page
      console.log(`Previewing suggestion ${suggestionId}`);
    }, 2000);
  };

  // Function to handle suggestion apply - actually implements the changes
  // This is locked for free users to encourage upgrades
  const handleApply = (suggestionId: string) => {
    if (mockUserPlan === 'free') {
      // Show upgrade prompt for free users
      alert('Upgrade to Pro to apply AI suggestions automatically!');
      return;
    }

    setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'applying' }));
    // Simulate apply process time
    setTimeout(() => {
      setSuggestionStates(prev => ({ ...prev, [suggestionId]: 'idle' }));
      // In a real app, this would make API calls to implement changes
      console.log(`Applied suggestion ${suggestionId}`);
    }, 3000);
  };

  // Function to get status badge for recent prompts with appropriate colors
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'applied':
        return <Badge tone="success" icon={CheckIcon}>Applied</Badge>;
      case 'previewed':
        return <Badge tone="attention" icon={ViewIcon}>Previewed</Badge>;
      case 'pending':
        return <Badge tone="info" icon={ClockIcon}>Pending</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Function to get plan badge with appropriate styling
  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case 'free':
        return <Badge tone="info">✨ Free</Badge>;
      case 'pro':
        return <Badge tone="attention">⚡ Pro</Badge>;
      case 'developer':
        return <Badge tone="success">🔧 Developer</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page
      title="Dashboard"
      subtitle="AI-powered store optimization at a glance"
      primaryAction={{
        content: "Chat with AI",
        onAction: () => console.log("Opening AI Assistant..."),
      }}
      secondaryActions={[
        {
          content: "View Analytics",
          onAction: () => console.log("View Analytics"),
        },
      ]}
    >
      <TitleBar title="AI Store Copilot - Dashboard" />

      <Layout>
        {/* Top Stats Row - Compact Overview */}
        <Layout.Section>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Card>
                <Box padding="300">
                  <BlockStack gap="200">
                    <InlineStack align="space-between" blockAlign="center">
                      <Icon source={ProductIcon} tone="base" />
                      <Text as="span" variant="headingLg" tone="success">
                        {mockStoreData.productCount}
                      </Text>
                    </InlineStack>
                    <Text as="p" variant="bodySm" tone="subdued">
                      Products
                    </Text>
                  </BlockStack>
                </Box>
              </Card>
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Card>
                <Box padding="300">
                  <BlockStack gap="200">
                    <InlineStack align="space-between" blockAlign="center">
                      <Icon source={OrderIcon} tone="base" />
                      <Text as="span" variant="headingLg" tone="success">
                        {mockStoreData.ordersThisMonth}
                      </Text>
                    </InlineStack>
                    <Text as="p" variant="bodySm" tone="subdued">
                      Orders this month
                    </Text>
                  </BlockStack>
                </Box>
              </Card>
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Card>
                <Box padding="300">
                  <BlockStack gap="200">
                    <InlineStack align="space-between" blockAlign="center">
                      <Icon source={CashDollarIcon} tone="base" />
                      <Text as="span" variant="headingLg" tone="success">
                        {mockStoreData.revenue}
                      </Text>
                    </InlineStack>
                    <Text as="p" variant="bodySm" tone="subdued">
                      Revenue
                    </Text>
                  </BlockStack>
                </Box>
              </Card>
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <Card>
                <Box padding="300">
                  <BlockStack gap="200">
                    <InlineStack align="space-between" blockAlign="center">
                      <Icon source={ViewIcon} tone="base" />
                      <Text as="span" variant="headingLg" tone="success">
                        {mockStoreData.conversionRate}
                      </Text>
                    </InlineStack>
                    <Text as="p" variant="bodySm" tone="subdued">
                      Conversion rate
                    </Text>
                  </BlockStack>
                </Box>
              </Card>
            </Grid.Cell>
          </Grid>
        </Layout.Section>

        {/* Main Content - Two Column Layout */}
        <Layout.Section>
          <Grid>
            {/* Left Column - AI Suggestions & Quick Actions */}
            <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 4, lg: 8, xl: 8 }}>
              <BlockStack gap="400">
                {/* 🤖 AI Suggestions - Compact Cards */}
                <Card>
                  <Box padding="400">
                    <BlockStack gap="400">
                      <InlineStack align="space-between" blockAlign="center">
                        <InlineStack gap="200" blockAlign="center">
                          <Icon source={MagicIcon} tone="base" />
                          <Text as="h2" variant="headingMd">
                            AI Suggestions
                          </Text>
                        </InlineStack>
                        <Badge tone="info">
                          {`${mockAISuggestions.length} ready`}
                        </Badge>
                      </InlineStack>

                      <BlockStack gap="300">
                        {mockAISuggestions.map((suggestion) => (
                          <Box
                            key={suggestion.id}
                            padding="300"
                            background="bg-surface-secondary"
                            borderRadius="200"
                          >
                            <BlockStack gap="200">
                              <InlineStack align="space-between" blockAlign="start">
                                <BlockStack gap="100">
                                  <InlineStack gap="200" blockAlign="center">
                                    <Text as="h3" variant="bodySm" fontWeight="semibold">
                                      {suggestion.title}
                                    </Text>
                                    <Badge
                                      tone={suggestion.impact === 'High' ? 'critical' : 'info'}
                                      size="small"
                                    >
                                      {suggestion.impact}
                                    </Badge>
                                  </InlineStack>
                                  <Text as="p" variant="bodyXs" tone="subdued">
                                    {suggestion.description.substring(0, 80)}...
                                  </Text>
                                </BlockStack>
                              </InlineStack>

                              <InlineStack gap="200" align="space-between">
                                <InlineStack gap="200">
                                  <Text as="span" variant="bodyXs" tone="subdued">
                                    ⏱️ {suggestion.estimatedTime}
                                  </Text>
                                  <Text as="span" variant="bodyXs" tone="subdued">
                                    📂 {suggestion.category}
                                  </Text>
                                </InlineStack>

                                <ButtonGroup>
                                  <Button
                                    size="micro"
                                    icon={ViewIcon}
                                    loading={suggestionStates[suggestion.id] === 'previewing'}
                                    onClick={() => handlePreview(suggestion.id)}
                                  >
                                    Preview
                                  </Button>
                                  <Button
                                    size="micro"
                                    variant="primary"
                                    icon={mockUserPlan === 'free' ? LockIcon : CheckIcon}
                                    loading={suggestionStates[suggestion.id] === 'applying'}
                                    onClick={() => handleApply(suggestion.id)}
                                  >
                                    {mockUserPlan === 'free' ? 'Pro' : 'Apply'}
                                  </Button>
                                </ButtonGroup>
                              </InlineStack>
                            </BlockStack>
                          </Box>
                        ))}
                      </BlockStack>

                      {/* Compact upgrade banner for free users */}
                      {mockUserPlan === 'free' && (
                        <CalloutCard
                          title="Unlock Auto-Apply"
                          illustration="https://cdn.shopify.com/s/files/1/0757/9955/files/empty-state.svg"
                          primaryAction={{
                            content: 'Upgrade to Pro',
                            onAction: () => console.log('Upgrade clicked'),
                          }}
                        >
                          <Text as="p" variant="bodyMd">
                            Apply AI suggestions automatically with Pro plan.
                          </Text>
                        </CalloutCard>
                      )}
                    </BlockStack>
                  </Box>
                </Card>

                {/* ⚡ Quick Actions - Compact Grid */}
                <Card>
                  <Box padding="400">
                    <BlockStack gap="300">
                      <InlineStack gap="200" blockAlign="center">
                        <Icon source={ClockIcon} tone="base" />
                        <Text as="h2" variant="headingMd">
                          Quick Actions
                        </Text>
                      </InlineStack>

                      <Grid>
                        {[
                          { action: "Improve homepage", icon: "🏠" },
                          { action: "Optimize SEO", icon: "🔍" },
                          { action: "Boost conversions", icon: "📈" },
                          { action: "Analyze performance", icon: "📊" },
                          { action: "Create campaigns", icon: "🎯" },
                          { action: "Review products", icon: "📦" },
                        ].map((item, index) => (
                          <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 3, md: 6, lg: 4, xl: 4 }}>
                            <Button
                              fullWidth
                              size="slim"
                              textAlign="left"
                              onClick={() => console.log(`Quick action: ${item.action}`)}
                            >
                              {`${item.icon} ${item.action}`}
                            </Button>
                          </Grid.Cell>
                        ))}
                      </Grid>
                    </BlockStack>
                  </Box>
                </Card>
              </BlockStack>
            </Grid.Cell>

            {/* Right Column - Recent Activity & Plan Info */}
            <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
              <BlockStack gap="400">
                {/* 🕒 Recent Activity - Compact List */}
                <Card>
                  <Box padding="400">
                    <BlockStack gap="300">
                      <InlineStack gap="200" blockAlign="center">
                        <Icon source={ClockIcon} tone="base" />
                        <Text as="h2" variant="headingMd">
                          Recent Activity
                        </Text>
                      </InlineStack>

                      <BlockStack gap="200">
                        {mockRecentPrompts.slice(0, 3).map((item) => (
                          <Box
                            key={item.id}
                            padding="200"
                            background="bg-surface-secondary"
                            borderRadius="100"
                          >
                            <BlockStack gap="100">
                              <InlineStack align="space-between" blockAlign="center">
                                <Text as="span" variant="bodyXs" fontWeight="semibold">
                                  {item.prompt.substring(0, 40)}...
                                </Text>
                                {getStatusBadge(item.status)}
                              </InlineStack>
                              <Text as="span" variant="bodyXs" tone="subdued">
                                {item.timestamp}
                              </Text>
                            </BlockStack>
                          </Box>
                        ))}
                      </BlockStack>

                      <Button fullWidth size="slim" variant="plain">
                        View all activity
                      </Button>
                    </BlockStack>
                  </Box>
                </Card>

                {/* 💼 Plan Info - Compact */}
                <Card>
                  <Box padding="400">
                    <BlockStack gap="300">
                      <InlineStack align="space-between" blockAlign="center">
                        <Text as="h2" variant="headingMd">
                          Your Plan
                        </Text>
                        {getPlanBadge(mockUserPlan)}
                      </InlineStack>

                      <BlockStack gap="200">
                        <Text as="p" variant="bodySm">
                          {currentUsage.total === -1
                            ? `${currentUsage.label}`
                            : `${currentUsage.used} of ${currentUsage.total} ${currentUsage.label}`
                          }
                        </Text>

                        {currentUsage.total !== -1 && (
                          <ProgressBar progress={usagePercentage} />
                        )}

                        {mockUserPlan === 'free' && (
                          <Button variant="primary" fullWidth size="slim">
                            Upgrade to Pro
                          </Button>
                        )}
                      </BlockStack>
                    </BlockStack>
                  </Box>
                </Card>

                {/* 📊 Store Snapshot - Compact */}
                <Card>
                  <Box padding="400">
                    <BlockStack gap="300">
                      <InlineStack gap="200" blockAlign="center">
                        <Icon source={ThemeIcon} tone="base" />
                        <Text as="h2" variant="headingMd">
                          Store Info
                        </Text>
                      </InlineStack>

                      <BlockStack gap="200">
                        <InlineStack align="space-between">
                          <Text as="span" variant="bodySm" tone="subdued">Apps</Text>
                          <Text as="span" variant="bodySm" fontWeight="semibold">
                            {mockStoreData.installedApps}
                          </Text>
                        </InlineStack>
                        <InlineStack align="space-between">
                          <Text as="span" variant="bodySm" tone="subdued">Theme</Text>
                          <Text as="span" variant="bodySm" fontWeight="semibold">
                            {mockStoreData.themeName}
                          </Text>
                        </InlineStack>
                      </BlockStack>
                    </BlockStack>
                  </Box>
                </Card>

                {/* 🧪 Developer JSON Viewer (only for developer mode) */}
                {mockMode === "developer" && (
                  <Card>
                    <Box padding="400">
                      <BlockStack gap="300">
                        <Text as="h2" variant="headingMd">
                          🧪 Debug Info
                        </Text>

                        <Box
                          padding="200"
                          background="bg-surface-secondary"
                          borderRadius="100"
                        >
                          <pre style={{
                            fontSize: '11px',
                            fontFamily: 'monospace',
                            margin: 0,
                            whiteSpace: 'pre-wrap',
                            overflow: 'auto'
                          }}>
                            {JSON.stringify(mockDeveloperData, null, 2)}
                          </pre>
                        </Box>
                      </BlockStack>
                    </Box>
                  </Card>
                )}
              </BlockStack>
            </Grid.Cell>
          </Grid>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
