import { useState } from "react";
import {
  Page,
  Layout,
  Card,
  <PERSON>,
  <PERSON>ton,
  Badge,
  ProgressBar,
  TextField,
  Checkbox,
  Select,
  InlineStack,
  BlockStack,
  Box,
  Icon,
  Tooltip,
  Divider,
} from "@shopify/polaris";
import {
  LockIcon,
  CheckIcon,
  SettingsIcon,
  NotificationIcon,
  ClockIcon,
  CreditCardIcon,
  CodeIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Settings() {
  // Mock plan data - In a real app, this would come from your backend/API
  const mockPlan: "free" | "pro" | "developer" = "pro"; // Change this to test different plans
  const mockUsage = { used: 7, total: 50 }; // Mock usage data
  const usagePercentage = (mockUsage.used / mockUsage.total) * 100;

  // Communication Channels State
  const [whatsappNumber, setWhatsappNumber] = useState("+****************");
  const [emailId, setEmailId] = useState("<EMAIL>");

  // AI Behavior Preferences State
  const [enableAutoSuggestions, setEnableAutoSuggestions] = useState(true);
  const [aiTone, setAiTone] = useState("friendly");
  const [sendSuggestionsAuto, setSendSuggestionsAuto] = useState(false);

  // Scheduled Reports State
  const [enableWeeklyReports, setEnableWeeklyReports] = useState(true);
  const [reportDay, setReportDay] = useState("monday");
  const [reportTime, setReportTime] = useState("09:00");

  // Developer Settings State (only for developer plan)
  const [enableRawJsonMode, setEnableRawJsonMode] = useState(false);
  const [enableDebugLogs, setEnableDebugLogs] = useState(true);

  // Mock handlers for testing functionality
  const handleSendWhatsAppTest = () => {
    alert(`Test message sent to WhatsApp: ${whatsappNumber}`);
  };

  const handleSendEmailTest = () => {
    alert(`Test email sent to: ${emailId}`);
  };

  const handleUpgradePlan = () => {
    alert("Redirecting to upgrade page...");
  };

  // Helper function to get plan badge
  const getPlanBadge = (plan: string) => {
    switch (plan) {
      case "free":
        return <Badge tone="info">✨ Free</Badge>;
      case "pro":
        return <Badge tone="success">⚡ Pro</Badge>;
      case "developer":
        return <Badge tone="attention">🔧 Developer</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // AI Tone options for dropdown
  const aiToneOptions = [
    { label: "Friendly", value: "friendly" },
    { label: "Professional", value: "professional" },
    { label: "Bold", value: "bold" },
    { label: "Minimal", value: "minimal" },
  ];

  // Day of week options for reports
  const dayOptions = [
    { label: "Monday", value: "monday" },
    { label: "Tuesday", value: "tuesday" },
    { label: "Wednesday", value: "wednesday" },
    { label: "Thursday", value: "thursday" },
    { label: "Friday", value: "friday" },
    { label: "Saturday", value: "saturday" },
    { label: "Sunday", value: "sunday" },
  ];

  return (
    <Page
      title="Settings"
      subtitle="Configure your AI assistant preferences and communication channels"
      primaryAction={{
        content: "Save Settings",
        onAction: () => alert("Settings saved successfully!"),
      }}
    >
      <TitleBar title="AI Store Copilot - Settings" />

      <Layout>
        {/* Communication Channels Section */}
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack gap="200" blockAlign="center">
                  <Icon source={NotificationIcon} tone="base" />
                  <Text as="h2" variant="headingMd">
                    Communication Channels
                  </Text>
                </InlineStack>

                <Text as="p" variant="bodyMd" tone="subdued">
                  Configure how you want to receive AI suggestions and notifications.
                </Text>

                <BlockStack gap="300">
                  {/* WhatsApp Configuration */}
                  <InlineStack gap="300" align="space-between" blockAlign="end">
                    <Box minWidth="300px">
                      <TextField
                        label="WhatsApp Number"
                        value={whatsappNumber}
                        onChange={setWhatsappNumber}
                        placeholder="+****************"
                        helpText="We'll send AI suggestions and alerts to this number"
                        autoComplete="tel"
                      />
                    </Box>
                    <Button
                      onClick={handleSendWhatsAppTest}
                      icon={CheckIcon}
                    >
                      Send Test
                    </Button>
                  </InlineStack>

                  {/* Email Configuration */}
                  <InlineStack gap="300" align="space-between" blockAlign="end">
                    <Box minWidth="300px">
                      <TextField
                        label="Email Address"
                        type="email"
                        value={emailId}
                        onChange={setEmailId}
                        placeholder="<EMAIL>"
                        helpText="Receive detailed reports and suggestions via email"
                        autoComplete="email"
                      />
                    </Box>
                    <Button
                      onClick={handleSendEmailTest}
                      icon={CheckIcon}
                    >
                      Send Test
                    </Button>
                  </InlineStack>
                </BlockStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        {/* AI Behavior Preferences Section */}
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <InlineStack gap="200" blockAlign="center">
                  <Icon source={SettingsIcon} tone="base" />
                  <Text as="h2" variant="headingMd">
                    AI Behavior Preferences
                  </Text>
                </InlineStack>

                <Text as="p" variant="bodyMd" tone="subdued">
                  Customize how your AI assistant behaves and communicates with you.
                </Text>

                <BlockStack gap="300">
                  {/* Enable AI Auto Suggestions */}
                  <Checkbox
                    label="Enable AI Auto Suggestions"
                    checked={enableAutoSuggestions}
                    onChange={setEnableAutoSuggestions}
                    helpText="Automatically generate optimization suggestions for your store"
                  />

                  {/* AI Tone Selection */}
                  <Box maxWidth="300px">
                    <Select
                      label="Default AI Tone"
                      options={aiToneOptions}
                      value={aiTone}
                      onChange={setAiTone}
                      helpText="Choose the communication style for AI responses"
                    />
                  </Box>

                  {/* Send Suggestions Automatically */}
                  {mockPlan === "free" ? (
                    <Tooltip content="Upgrade to Pro to enable automatic suggestions">
                      <Box>
                        <Checkbox
                          label="Send Suggestions Automatically"
                          checked={false}
                          disabled
                          helpText="Automatically send suggestions via WhatsApp/Email (Pro feature)"
                        />
                      </Box>
                    </Tooltip>
                  ) : (
                    <Checkbox
                      label="Send Suggestions Automatically"
                      checked={sendSuggestionsAuto}
                      onChange={setSendSuggestionsAuto}
                      helpText="Automatically send suggestions via WhatsApp/Email"
                    />
                  )}
                </BlockStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>

        {/* Two Column Layout for Reports and Plan */}
        <Layout.Section>
          <InlineStack gap="400" align="start">
            {/* Scheduled Reports Section */}
            <Box width="50%">
              <Card>
                <Box padding="400">
                  <BlockStack gap="400">
                    <InlineStack gap="200" blockAlign="center">
                      <Icon source={ClockIcon} tone="base" />
                      <Text as="h2" variant="headingMd">
                        Scheduled Reports
                      </Text>
                    </InlineStack>

                    <Text as="p" variant="bodyMd" tone="subdued">
                      Get regular performance reports delivered to your inbox.
                    </Text>

                    <BlockStack gap="300">
                      {/* Enable Weekly Reports */}
                      <Checkbox
                        label="Enable Weekly Reports"
                        checked={enableWeeklyReports}
                        onChange={setEnableWeeklyReports}
                        helpText="Receive comprehensive store performance reports"
                      />

                      {/* Report Configuration (only if enabled) */}
                      {enableWeeklyReports && (
                        <BlockStack gap="300">
                          <Box maxWidth="200px">
                            <Select
                              label="Day of Week"
                              options={dayOptions}
                              value={reportDay}
                              onChange={setReportDay}
                            />
                          </Box>

                          <Box maxWidth="150px">
                            <TextField
                              label="Time"
                              type="time"
                              value={reportTime}
                              onChange={setReportTime}
                              helpText="24-hour format"
                              autoComplete="off"
                            />
                          </Box>
                        </BlockStack>
                      )}
                    </BlockStack>
                  </BlockStack>
                </Box>
              </Card>
            </Box>

            {/* Plan & Usage Section */}
            <Box width="50%">
              <Card>
                <Box padding="400">
                  <BlockStack gap="400">
                    <InlineStack gap="200" blockAlign="center">
                      <Icon source={CreditCardIcon} tone="base" />
                      <Text as="h2" variant="headingMd">
                        Plan & Usage
                      </Text>
                    </InlineStack>

                    <BlockStack gap="300">
                      {/* Current Plan */}
                      <InlineStack align="space-between" blockAlign="center">
                        <Text as="span" variant="bodyMd">Current Plan</Text>
                        {getPlanBadge(mockPlan)}
                      </InlineStack>

                      {/* Usage Progress */}
                      <BlockStack gap="200">
                        <InlineStack align="space-between">
                          <Text as="span" variant="bodySm">Prompts Used</Text>
                          <Text as="span" variant="bodySm">
                            {mockUsage.used} of {mockUsage.total}
                          </Text>
                        </InlineStack>
                        <ProgressBar progress={usagePercentage} />
                      </BlockStack>

                      {/* Plan Features */}
                      <BlockStack gap="100">
                        <Text as="p" variant="bodySm" tone="subdued">
                          {mockPlan === "free" && "• 10 AI prompts per month"}
                          {mockPlan === "pro" && "• 50 AI prompts per month"}
                          {mockPlan === "developer" && "• Unlimited AI prompts"}
                        </Text>
                        <Text as="p" variant="bodySm" tone="subdued">
                          {mockPlan === "free" && "• Basic AI suggestions"}
                          {mockPlan === "pro" && "• Auto-apply suggestions"}
                          {mockPlan === "developer" && "• API access & debug tools"}
                        </Text>
                      </BlockStack>

                      {/* Upgrade Button (if not on Developer plan) */}
                      {mockPlan !== "developer" && (
                        <Button
                          variant="primary"
                          fullWidth
                          onClick={handleUpgradePlan}
                        >
                          {mockPlan === "free" ? "Upgrade to Pro" : "Upgrade to Developer"}
                        </Button>
                      )}
                    </BlockStack>
                  </BlockStack>
                </Box>
              </Card>
            </Box>
          </InlineStack>
        </Layout.Section>

        {/* Developer Settings Section (only for developer plan) */}
        {mockPlan === "developer" && (
          <Layout.Section>
            <Card>
              <Box padding="400">
                <BlockStack gap="400">
                  <InlineStack gap="200" blockAlign="center">
                    <Icon source={CodeIcon} tone="base" />
                    <Text as="h2" variant="headingMd">
                      Developer Settings
                    </Text>
                  </InlineStack>

                  <Text as="p" variant="bodyMd" tone="subdued">
                    Advanced settings for developers and technical users.
                  </Text>

                  <BlockStack gap="300">
                    {/* Enable Raw JSON Mode */}
                    <Checkbox
                      label="Enable Raw JSON Mode"
                      checked={enableRawJsonMode}
                      onChange={setEnableRawJsonMode}
                      helpText="Show raw JSON responses from AI for debugging"
                    />

                    {/* Enable Debug Logs */}
                    <Checkbox
                      label="Enable Debug Logs"
                      checked={enableDebugLogs}
                      onChange={setEnableDebugLogs}
                      helpText="Log detailed information for troubleshooting"
                    />

                    <Divider />

                    {/* API Information */}
                    <BlockStack gap="200">
                      <Text as="h3" variant="headingSm">
                        API Access
                      </Text>

                      <TextField
                        label="Webhook URL"
                        value="https://your-app.com/webhooks/ai-suggestions"
                        readOnly
                        helpText="Use this URL to receive AI suggestions via webhook"
                        autoComplete="off"
                      />

                      <TextField
                        label="API Key"
                        value="sk-1234567890abcdef..."
                        type="password"
                        readOnly
                        helpText="Your API key for programmatic access"
                        autoComplete="off"
                      />
                    </BlockStack>
                  </BlockStack>
                </BlockStack>
              </Box>
            </Card>
          </Layout.Section>
        )}
      </Layout>
    </Page>
  );
}
