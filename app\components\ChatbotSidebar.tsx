import { useState, useRef, useEffect } from "react";
import {
  Card,
  TextField,
  Button,
  Icon,
  Text,
  BlockStack,
  InlineStack,
  Box,
  Scrollable,
  Divider,
} from "@shopify/polaris";
import {
  ChatIcon,
  SendIcon,
  AttachmentIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
} from "@shopify/polaris-icons";
import styles from "./ChatbotSidebar.module.css";

// Define the structure for chat messages
interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  status?: "sending" | "sent" | "delivered" | "read" | "error";
  isMarkdown?: boolean;
}

// Define the user mode type
type UserMode = "free" | "pro" | "developer";

/**
 * ChatbotSidebar Component
 * 
 * A fixed sidebar that acts as a Shopify Copilot, always available to merchants
 * regardless of which page they're on in the Shopify admin.
 * 
 * Features:
 * - Fixed positioning to the right side of the browser
 * - Collapsible/expandable
 * - Three user modes (Free, Pro, Developer)
 * - Chat interface with message history
 * - File attachment support
 * - Responsive design
 */
export function ChatbotSidebar() {
  // State for controlling sidebar visibility (collapsed/expanded)
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // State for the current user input
  const [currentMessage, setCurrentMessage] = useState("");
  
  // Mock user mode - in a real app, this would come from user settings/subscription
  const [userMode] = useState<UserMode>("pro");
  
  // State for chat messages - start with empty for better demo of empty state
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  // State for first-time user experience
  const [isFirstTime, setIsFirstTime] = useState(true);

  // State for typing indicator
  const [isTyping, setIsTyping] = useState(false);

  // State for input focus and character count
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);

  // State for advanced features
  const [showQuickReplies, setShowQuickReplies] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);

  // Ref for the messages container to enable auto-scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Function to get the appropriate badge color and text for user mode
  const getModeDisplay = (mode: UserMode) => {
    switch (mode) {
      case "free":
        return {
          tone: "info" as const,
          text: "✨ Free",
          description: "Basic AI assistance",
          icon: "✨",
          color: "#6366f1",
          bgColor: "#f0f9ff"
        };
      case "pro":
        return {
          tone: "attention" as const,
          text: "⚡ Pro",
          description: "Advanced features",
          icon: "⚡",
          color: "#f59e0b",
          bgColor: "#fffbeb"
        };
      case "developer":
        return {
          tone: "success" as const,
          text: "🔧 Developer",
          description: "Full access",
          icon: "🔧",
          color: "#10b981",
          bgColor: "#f0fdf4"
        };
      default:
        return {
          tone: "info" as const,
          text: "✨ Free",
          description: "Basic AI assistance",
          icon: "✨",
          color: "#6366f1",
          bgColor: "#f0f9ff"
        };
    }
  };

  // Function to handle sending a new message
  const handleSendMessage = () => {
    if (!currentMessage.trim() || isTyping) return;

    // Create new user message with sending status
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      type: "user",
      content: currentMessage.trim(),
      timestamp: new Date(),
      status: "sending",
    };

    // Add user message to chat
    setMessages(prev => [...prev, newUserMessage]);

    // Clear input field
    setCurrentMessage("");

    // Update message status to sent after a short delay
    setTimeout(() => {
      setMessages(prev => prev.map(msg =>
        msg.id === newUserMessage.id
          ? { ...msg, status: "sent" as const }
          : msg
      ));
    }, 500);

    // Update to delivered status
    setTimeout(() => {
      setMessages(prev => prev.map(msg =>
        msg.id === newUserMessage.id
          ? { ...msg, status: "delivered" as const }
          : msg
      ));
    }, 1000);

    // Show typing indicator
    setIsTyping(true);

    // Simulate AI response with realistic delay
    const responseDelay = Math.random() * 2000 + 1500; // 1.5-3.5 seconds
    setTimeout(() => {
      setIsTyping(false);

      // Mark user message as read when AI responds
      setMessages(prev => prev.map(msg =>
        msg.id === newUserMessage.id
          ? { ...msg, status: "read" as const }
          : msg
      ));

      // Generate more contextual responses based on user input
      const userInput = newUserMessage.content.toLowerCase();
      let aiResponse = "";

      if (userInput.includes("product") || userInput.includes("description")) {
        aiResponse = "I can help you optimize your product descriptions! Here are some quick wins:\n\n🎯 **Use power words** like 'exclusive', 'limited', 'premium'\n📊 **Add specific details** - sizes, materials, dimensions\n💡 **Include use cases** - when and how to use the product\n⭐ **Highlight unique selling points**\n\nWant me to analyze a specific product for you?";
      } else if (userInput.includes("seo") || userInput.includes("search")) {
        aiResponse = "Great question about SEO! Here's how to boost your store's search visibility:\n\n🔍 **Keyword research** - Use tools like Google Keyword Planner\n📝 **Optimize product titles** - Include main keywords naturally\n🏷️ **Meta descriptions** - Write compelling 150-160 character summaries\n📸 **Alt text for images** - Describe your product images\n🔗 **Internal linking** - Connect related products\n\nShould I audit your current SEO setup?";
      } else if (userInput.includes("conversion") || userInput.includes("sales")) {
        aiResponse = "Let's boost your conversion rate! Here are proven strategies:\n\n💳 **Simplify checkout** - Reduce steps and form fields\n🛡️ **Add trust signals** - Security badges, reviews, guarantees\n⚡ **Improve page speed** - Optimize images and code\n📱 **Mobile optimization** - Ensure smooth mobile experience\n🎁 **Create urgency** - Limited time offers, stock counters\n\nWant me to analyze your current conversion funnel?";
      } else {
        aiResponse = `I understand you're asking about "${newUserMessage.content}". I'm here to help with all aspects of your Shopify store!\n\n🛍️ **Store optimization**\n📈 **Sales strategies** \n🎨 **Design improvements**\n📊 **Analytics insights**\n🔧 **Technical support**\n\nCould you tell me more specifically what you'd like to improve? I can provide more targeted advice!`;
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: aiResponse,
        timestamp: new Date(),
        status: "delivered",
        isMarkdown: true,
      };
      setMessages(prev => [...prev, aiMessage]);
    }, responseDelay);
  };

  // Enhanced keyboard navigation
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    } else if (event.key === "Escape") {
      // Close attachment menu if open
      if (showAttachmentMenu) {
        setShowAttachmentMenu(false);
      }
      // Clear input if it has content
      else if (currentMessage.trim()) {
        setCurrentMessage("");
      }
      // Collapse sidebar if expanded
      else if (!isCollapsed) {
        setIsCollapsed(true);
      }
    }
  };

  // Handle keyboard navigation for quick start suggestions
  const handleQuickStartKeyPress = (event: React.KeyboardEvent, prompt: string) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleQuickStart(prompt);
    }
  };

  // Function to handle file attachment with enhanced UI
  const handleAttachment = () => {
    setShowAttachmentMenu(!showAttachmentMenu);
  };

  // Function to handle specific attachment types
  const handleAttachmentType = (type: string) => {
    setShowAttachmentMenu(false);
    switch (type) {
      case "image":
        // TODO: Implement image upload
        console.log("Image upload clicked");
        break;
      case "document":
        // TODO: Implement document upload
        console.log("Document upload clicked");
        break;
      case "link":
        // TODO: Implement link sharing
        console.log("Link sharing clicked");
        break;
      default:
        console.log("Unknown attachment type");
    }
  };

  // Get character count and limits based on user mode
  const getCharacterLimits = () => {
    switch (userMode) {
      case "free":
        return { limit: 500, warning: 450 };
      case "pro":
        return { limit: 2000, warning: 1800 };
      case "developer":
        return { limit: 5000, warning: 4500 };
      default:
        return { limit: 500, warning: 450 };
    }
  };

  const characterLimits = getCharacterLimits();
  const currentLength = currentMessage.length;
  const isNearLimit = currentLength >= characterLimits.warning;
  const isOverLimit = currentLength > characterLimits.limit;

  // Quick start suggestions for empty state
  const quickStartSuggestions = [
    {
      icon: "📈",
      title: "Boost Sales",
      description: "Get tips to increase conversions",
      prompt: "How can I increase my store's conversion rate?"
    },
    {
      icon: "🎨",
      title: "Improve Design",
      description: "Enhance your store's appearance",
      prompt: "What design improvements can I make to my store?"
    },
    {
      icon: "📝",
      title: "Product Descriptions",
      description: "Write better product copy",
      prompt: "Help me write compelling product descriptions"
    },
    {
      icon: "🔍",
      title: "SEO Optimization",
      description: "Improve search visibility",
      prompt: "How can I improve my store's SEO?"
    }
  ];

  // Function to handle quick start suggestion
  const handleQuickStart = (prompt: string) => {
    setCurrentMessage(prompt);
    setIsFirstTime(false);
    // Auto-send the message
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  // Quick reply suggestions based on context
  const getQuickReplies = () => {
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.type === "user") return [];

    const content = lastMessage.content.toLowerCase();

    if (content.includes("seo") || content.includes("search")) {
      return ["Tell me more about SEO", "How do I optimize images?", "What about meta descriptions?"];
    } else if (content.includes("conversion") || content.includes("sales")) {
      return ["Show me examples", "What about mobile users?", "How do I track this?"];
    } else if (content.includes("product") || content.includes("description")) {
      return ["Give me a template", "What about pricing strategy?", "How do I add urgency?"];
    }

    return ["That's helpful!", "Tell me more", "What's next?", "Can you give an example?"];
  };

  // Function to handle message deletion
  const handleDeleteMessage = (messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  // Function to export chat history
  const handleExportChat = () => {
    const chatText = messages.map(msg =>
      `[${msg.timestamp.toLocaleString()}] ${msg.type === "user" ? "You" : "AI Assistant"}: ${msg.content}`
    ).join('\n\n');

    const blob = new Blob([chatText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-history-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Function to search messages
  const searchMessages = (query: string) => {
    if (!query.trim()) return messages;
    return messages.filter(msg =>
      msg.content.toLowerCase().includes(query.toLowerCase())
    );
  };

  // Function to toggle sidebar collapse/expand
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Function to get status icon for messages
  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "sending":
        return "⏳";
      case "sent":
        return "✓";
      case "delivered":
        return "✓✓";
      case "read":
        return "✓✓";
      case "error":
        return "❌";
      default:
        return "";
    }
  };

  // Function to format message content with basic markdown support
  const formatMessageContent = (content: string, isMarkdown?: boolean) => {
    if (!isMarkdown) return content;

    // Basic markdown formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/•/g, '•');
  };

  // Get mode display properties
  const modeDisplay = getModeDisplay(userMode);

  return (
    <>
      {/*
        Fixed positioning container that stays on the right side of the browser
        - Uses CSS classes for better styling and responsive behavior
        - position: fixed keeps it in place during scrolling
        - right: 0 positions it at the right edge
        - top: 0 and bottom: 0 make it full height
        - z-index: 1000 ensures it stays above other content
      */}
      <aside
        className={`${styles.sidebarContainer} ${
          isCollapsed ? styles.collapsed : styles.expanded
        }`}
        role="complementary"
        aria-label="AI Assistant Chatbot"
        aria-expanded={!isCollapsed}
      >
        {/* Skip link for accessibility */}
        <a href="#main-content" className={styles.skipLink}>
          Skip to main content
        </a>

        {/* Main sidebar card */}
        <Card>
          <div className={styles.sidebarContent}>
            {/* Enhanced header section with branding and mode badge */}
            <div className={styles.sidebarHeader}>
              <Box padding="300">
              <InlineStack align="space-between" blockAlign="center">
                {!isCollapsed && (
                  <BlockStack gap="200">
                    {/* Main header with app branding */}
                    <InlineStack gap="200" blockAlign="center">
                      <div className={styles.appIcon}>
                        <Icon source={ChatIcon} tone="base" />
                      </div>
                      <BlockStack gap="050">
                        <Text as="h3" variant="headingSm" fontWeight="semibold">
                          AI Store Copilot
                        </Text>
                        <Text as="p" variant="bodySm" tone="subdued">
                          Your intelligent assistant
                        </Text>
                      </BlockStack>
                    </InlineStack>

                    {/* Enhanced mode badge with description */}
                    <div className={styles.modeBadgeContainer}>
                      <InlineStack align="space-between" blockAlign="center">
                        <div
                          className={styles.customModeBadge}
                          style={{
                            backgroundColor: modeDisplay.bgColor,
                            borderColor: modeDisplay.color,
                          }}
                        >
                          <InlineStack gap="100" blockAlign="center">
                            <span className={styles.modeIcon}>{modeDisplay.icon}</span>
                            <BlockStack gap="025">
                              <Text as="span" variant="bodySm" fontWeight="medium">
                                {modeDisplay.text.replace(/[✨⚡🔧]\s/, '')}
                              </Text>
                              <Text as="span" variant="bodyXs" tone="subdued">
                                {modeDisplay.description}
                              </Text>
                            </BlockStack>
                          </InlineStack>
                        </div>

                        {/* Header actions */}
                        <InlineStack gap="100">
                          <button
                            className={styles.headerAction}
                            onClick={() => setShowSearch(!showSearch)}
                            title="Search messages"
                            aria-label="Search messages"
                          >
                            🔍
                          </button>
                          {messages.length > 0 && (
                            <button
                              className={styles.headerAction}
                              onClick={handleExportChat}
                              title="Export chat history"
                              aria-label="Export chat history"
                            >
                              📥
                            </button>
                          )}
                        </InlineStack>
                      </InlineStack>
                    </div>
                  </BlockStack>
                )}

                {/* Enhanced collapse/expand toggle button */}
                <Button
                  variant="tertiary"
                  size="slim"
                  icon={isCollapsed ? ChevronLeftIcon : ChevronRightIcon}
                  onClick={toggleSidebar}
                  accessibilityLabel={isCollapsed ? "Expand AI assistant" : "Collapse AI assistant"}
                />
              </InlineStack>
              </Box>
            </div>

            {/* Only show chat content when sidebar is expanded */}
            {!isCollapsed && (
              <>
                {/* Messages area - scrollable container with empty state */}
                <div
                  className={styles.messagesContainer}
                  role="log"
                  aria-label="Chat conversation"
                  aria-live="polite"
                  aria-atomic="false"
                >
                  <Scrollable style={{ height: "100%" }}>
                    <Box padding="300">
                      <BlockStack gap="300">
                        {/* Empty state with welcome message and quick start suggestions */}
                        {messages.length === 0 && (
                          <div className={styles.emptyState}>
                            <BlockStack gap="400" align="center">
                              {/* Welcome section */}
                              <div className={styles.welcomeSection}>
                                <div className={styles.welcomeIcon}>🤖</div>
                                <BlockStack gap="200" align="center">
                                  <Text as="h3" variant="headingMd" alignment="center">
                                    Welcome to AI Store Copilot!
                                  </Text>
                                  <Text as="p" variant="bodyMd" tone="subdued" alignment="center">
                                    I'm here to help you optimize your Shopify store, boost sales, and improve customer experience. Let's get started!
                                  </Text>
                                </BlockStack>
                              </div>

                              {/* Quick start suggestions with enhanced accessibility */}
                              <div className={styles.quickStartGrid} role="group" aria-label="Quick start suggestions">
                                {quickStartSuggestions.map((suggestion, index) => (
                                  <button
                                    key={index}
                                    className={styles.quickStartCard}
                                    onClick={() => handleQuickStart(suggestion.prompt)}
                                    onKeyDown={(e) => handleQuickStartKeyPress(e, suggestion.prompt)}
                                    aria-label={`${suggestion.title}: ${suggestion.description}`}
                                    tabIndex={0}
                                  >
                                    <BlockStack gap="100">
                                      <div className={styles.suggestionIcon} aria-hidden="true">
                                        {suggestion.icon}
                                      </div>
                                      <Text as="span" variant="bodySm" fontWeight="medium">
                                        {suggestion.title}
                                      </Text>
                                      <Text as="span" variant="bodyXs" tone="subdued">
                                        {suggestion.description}
                                      </Text>
                                    </BlockStack>
                                  </button>
                                ))}
                              </div>

                              {/* Additional help */}
                              <div className={styles.helpSection}>
                                <Text as="p" variant="bodyXs" tone="subdued" alignment="center">
                                  💡 Tip: You can ask me anything about your store, products, marketing, or Shopify features
                                </Text>
                              </div>
                            </BlockStack>
                          </div>
                        )}

                        {/* Regular messages */}
                        {messages.map((message) => (
                          <div key={message.id} className={styles.messageContainer}>
                            {/* Enhanced message bubble with better design */}
                            <div
                              className={`${styles.messageBubble} ${
                                message.type === "user"
                                  ? styles.userMessage
                                  : styles.assistantMessage
                              }`}
                            >
                              <Box padding="300">
                              <BlockStack gap="150">
                                {/* Message header with sender and status */}
                                <InlineStack align="space-between" blockAlign="center">
                                  <InlineStack gap="100" blockAlign="center">
                                    {/* Avatar placeholder */}
                                    <div className={`${styles.messageAvatar} ${
                                      message.type === "user" ? styles.userAvatar : styles.assistantAvatar
                                    }`}>
                                      {message.type === "user" ? "👤" : "🤖"}
                                    </div>
                                    <Text
                                      as="span"
                                      variant="bodySm"
                                      fontWeight="medium"
                                      tone={message.type === "user" ? "text-inverse" : "subdued"}
                                    >
                                      {message.type === "user" ? "You" : "AI Assistant"}
                                    </Text>
                                  </InlineStack>

                                  {/* Status indicator for user messages */}
                                  {message.type === "user" && message.status && (
                                    <span
                                      className={styles.statusIcon}
                                      title={`Message ${message.status}`}
                                    >
                                      {getStatusIcon(message.status)}
                                    </span>
                                  )}
                                </InlineStack>

                                {/* Enhanced message content */}
                                <div className={styles.messageContent}>
                                  {message.isMarkdown ? (
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: formatMessageContent(message.content, true)
                                      }}
                                      className={styles.markdownContent}
                                    />
                                  ) : (
                                    <div style={{ whiteSpace: "pre-wrap" }}>
                                      <Text
                                        as="p"
                                        variant="bodyMd"
                                        tone={message.type === "user" ? "text-inverse" : undefined}
                                      >
                                        {message.content}
                                      </Text>
                                    </div>
                                  )}
                                </div>

                                {/* Enhanced timestamp with better styling */}
                                <InlineStack align="space-between" blockAlign="center">
                                  <Text
                                    as="span"
                                    variant="bodyXs"
                                    tone={message.type === "user" ? "text-inverse" : "subdued"}
                                  >
                                    {message.timestamp.toLocaleTimeString([], {
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </Text>

                                  {/* Enhanced message actions */}
                                  <div className={styles.messageActions} role="group" aria-label="Message actions">
                                    <button
                                      className={styles.actionButton}
                                      title="Copy message to clipboard"
                                      aria-label="Copy message to clipboard"
                                      onClick={() => {
                                        navigator.clipboard.writeText(message.content);
                                        // TODO: Add toast notification for copy success
                                      }}
                                      tabIndex={0}
                                    >
                                      📋
                                    </button>
                                    <button
                                      className={styles.actionButton}
                                      title="Delete message"
                                      aria-label="Delete message"
                                      onClick={() => handleDeleteMessage(message.id)}
                                      tabIndex={0}
                                    >
                                      🗑️
                                    </button>
                                  </div>
                                </InlineStack>
                              </BlockStack>
                              </Box>
                            </div>
                          </div>
                        ))}

                        {/* Enhanced typing indicator when AI is responding */}
                        {isTyping && (
                          <div className={styles.messageContainer}>
                            <div className={`${styles.messageBubble} ${styles.assistantMessage} ${styles.typingMessage}`}>
                              <Box padding="300">
                                <BlockStack gap="150">
                                  {/* Header with avatar */}
                                  <InlineStack gap="100" blockAlign="center">
                                    <div className={`${styles.messageAvatar} ${styles.assistantAvatar}`}>
                                      🤖
                                    </div>
                                    <Text as="span" variant="bodySm" fontWeight="medium" tone="subdued">
                                      AI Assistant
                                    </Text>
                                  </InlineStack>

                                  {/* Enhanced typing animation */}
                                  <div className={styles.enhancedTypingIndicator}>
                                    <div className={styles.typingAnimation}>
                                      <div className={styles.typingDot}></div>
                                      <div className={styles.typingDot}></div>
                                      <div className={styles.typingDot}></div>
                                    </div>
                                    <div className={styles.typingText}>
                                      <Text as="span" variant="bodySm" tone="subdued">
                                        Analyzing your request...
                                      </Text>
                                    </div>
                                  </div>
                                </BlockStack>
                              </Box>
                            </div>
                          </div>
                        )}

                        {/* Invisible div to enable auto-scrolling to bottom */}
                        <div ref={messagesEndRef} />
                      </BlockStack>
                    </Box>
                  </Scrollable>
                </div>

                <Divider />

                {/* Enhanced input area for new messages */}
                <div className={styles.inputArea}>
                  <Box padding="300">
                  <BlockStack gap="200">
                    {/* Enhanced text input with attachment menu and send button */}
                    <div className={styles.inputContainer}>
                      <div
                        className={`${styles.inputWrapper} ${isInputFocused ? styles.inputFocused : ''}`}
                        onKeyDown={handleKeyPress}
                      >
                        <TextField
                          label=""
                          value={currentMessage}
                          onChange={setCurrentMessage}
                          onFocus={() => setIsInputFocused(true)}
                          onBlur={() => setIsInputFocused(false)}
                          placeholder="Ask me anything about your store..."
                          multiline={3}
                          autoComplete="off"
                          maxLength={characterLimits.limit}
                          connectedLeft={
                            <div className={styles.attachmentContainer}>
                              <Button
                                variant="tertiary"
                                icon={AttachmentIcon}
                                onClick={handleAttachment}
                                accessibilityLabel="Attach file"
                                pressed={showAttachmentMenu}
                              />
                              {/* Attachment menu with enhanced accessibility */}
                              {showAttachmentMenu && (
                                <div
                                  className={styles.attachmentMenu}
                                  role="menu"
                                  aria-label="Attachment options"
                                >
                                  <button
                                    className={styles.attachmentOption}
                                    onClick={() => handleAttachmentType("image")}
                                    role="menuitem"
                                    aria-label="Attach image file"
                                    tabIndex={0}
                                  >
                                    <span aria-hidden="true">🖼️</span> Image
                                  </button>
                                  <button
                                    className={styles.attachmentOption}
                                    onClick={() => handleAttachmentType("document")}
                                    role="menuitem"
                                    aria-label="Attach document file"
                                    tabIndex={0}
                                  >
                                    <span aria-hidden="true">📄</span> Document
                                  </button>
                                  <button
                                    className={styles.attachmentOption}
                                    onClick={() => handleAttachmentType("link")}
                                    role="menuitem"
                                    aria-label="Share a link"
                                    tabIndex={0}
                                  >
                                    <span aria-hidden="true">🔗</span> Link
                                  </button>
                                </div>
                              )}
                            </div>
                          }
                          connectedRight={
                            <Button
                              variant="primary"
                              icon={SendIcon}
                              onClick={handleSendMessage}
                              disabled={!currentMessage.trim() || isTyping || isOverLimit}
                              accessibilityLabel="Send message"
                              tone={isOverLimit ? "critical" : undefined}
                            />
                          }
                        />
                      </div>

                      {/* Character count and status */}
                      <div className={styles.inputStatus}>
                        <InlineStack align="space-between" blockAlign="center">
                          <Text
                            as="span"
                            variant="bodyXs"
                            tone={isNearLimit ? (isOverLimit ? "critical" : "caution") : "subdued"}
                          >
                            {currentLength}/{characterLimits.limit} characters
                          </Text>

                          {/* Quick actions */}
                          <InlineStack gap="100">
                            {currentMessage.length > 0 && (
                              <button
                                className={styles.quickAction}
                                onClick={() => setCurrentMessage("")}
                                title="Clear message"
                              >
                                🗑️
                              </button>
                            )}
                            <button
                              className={styles.quickAction}
                              onClick={() => setCurrentMessage(currentMessage + " 🤔")}
                              title="Add thinking emoji"
                            >
                              🤔
                            </button>
                          </InlineStack>
                        </InlineStack>
                      </div>
                    </div>

                    {/* Quick reply suggestions */}
                    {messages.length > 0 && !isTyping && getQuickReplies().length > 0 && (
                      <div className={styles.quickRepliesContainer}>
                        <Text as="span" variant="bodyXs" tone="subdued">
                          Quick replies:
                        </Text>
                        <div className={styles.quickReplies}>
                          {getQuickReplies().slice(0, 3).map((reply, index) => (
                            <button
                              key={index}
                              className={styles.quickReplyButton}
                              onClick={() => {
                                setCurrentMessage(reply);
                                setShowQuickReplies(false);
                              }}
                              aria-label={`Quick reply: ${reply}`}
                            >
                              {reply}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Enhanced helper text based on user mode */}
                    <div className={styles.helperText}>
                      <InlineStack align="space-between" blockAlign="center">
                        <Text as="span" variant="bodySm" tone="subdued">
                          {userMode === "free" && "5 messages remaining today"}
                          {userMode === "pro" && "Unlimited messages • Preview & Apply"}
                          {userMode === "developer" && "Full access • Debug tools • API"}
                        </Text>

                        {isTyping && (
                          <Text as="span" variant="bodySm" tone="subdued">
                            AI is typing...
                          </Text>
                        )}
                      </InlineStack>
                    </div>
                  </BlockStack>
                  </Box>
                </div>
              </>
            )}
          </div>
        </Card>
      </aside>
    </>
  );
}
