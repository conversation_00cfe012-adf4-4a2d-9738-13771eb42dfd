import { useState, useRef, useEffect } from "react";
import {
  Card,
  TextField,
  Button,
  Badge,
  Icon,
  Text,
  BlockStack,
  InlineStack,
  Box,
  Scrollable,
  Divider,
} from "@shopify/polaris";
import {
  ChatIcon,
  SendIcon,
  AttachmentIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
} from "@shopify/polaris-icons";
import styles from "./ChatbotSidebar.module.css";

// Define the structure for chat messages
interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

// Define the user mode type
type UserMode = "free" | "pro" | "developer";

/**
 * ChatbotSidebar Component
 * 
 * A fixed sidebar that acts as a Shopify Copilot, always available to merchants
 * regardless of which page they're on in the Shopify admin.
 * 
 * Features:
 * - Fixed positioning to the right side of the browser
 * - Collapsible/expandable
 * - Three user modes (Free, Pro, Developer)
 * - Chat interface with message history
 * - File attachment support
 * - Responsive design
 */
export function ChatbotSidebar() {
  // State for controlling sidebar visibility (collapsed/expanded)
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // State for the current user input
  const [currentMessage, setCurrentMessage] = useState("");
  
  // Mock user mode - in a real app, this would come from user settings/subscription
  const [userMode] = useState<UserMode>("pro");
  
  // State for chat messages with some dummy data for testing
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      type: "assistant",
      content: "👋 Hi there! I'm your AI assistant for Shopify. I'm here to help you optimize your store, improve your products, and boost your sales. What would you like to work on today?",
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
    },
    {
      id: "2",
      type: "user",
      content: "How can I improve my product descriptions to increase conversions?",
      timestamp: new Date(Date.now() - 240000), // 4 minutes ago
    },
    {
      id: "3",
      type: "assistant",
      content: "Excellent question! Here are proven strategies to boost conversions through better product descriptions:\n\n✨ **Focus on benefits over features**\n• Instead of \"100% cotton\" → \"Soft, breathable fabric that keeps you comfortable all day\"\n\n🎯 **Use emotional triggers**\n• \"Transform your morning routine\" vs \"Coffee maker\"\n\n📈 **Include social proof**\n• \"Loved by 10,000+ customers\"\n• Add customer quotes and ratings\n\n🔍 **SEO optimization**\n• Include relevant keywords naturally\n• Use bullet points for scannability\n\nWould you like me to review a specific product and suggest improvements?",
      timestamp: new Date(Date.now() - 180000), // 3 minutes ago
    },
  ]);

  // State for typing indicator
  const [isTyping, setIsTyping] = useState(false);

  // Ref for the messages container to enable auto-scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Function to get the appropriate badge color and text for user mode
  const getModeDisplay = (mode: UserMode) => {
    switch (mode) {
      case "free":
        return { tone: "info" as const, text: "✨ Free", description: "Basic AI assistance" };
      case "pro":
        return { tone: "attention" as const, text: "⚡ Pro", description: "Advanced features" };
      case "developer":
        return { tone: "success" as const, text: "� Developer", description: "Full access" };
      default:
        return { tone: "info" as const, text: "✨ Free", description: "Basic AI assistance" };
    }
  };

  // Function to handle sending a new message
  const handleSendMessage = () => {
    if (!currentMessage.trim() || isTyping) return;

    // Create new user message
    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      type: "user",
      content: currentMessage.trim(),
      timestamp: new Date(),
    };

    // Add user message to chat
    setMessages(prev => [...prev, newUserMessage]);

    // Clear input field
    setCurrentMessage("");

    // Show typing indicator
    setIsTyping(true);

    // Simulate AI response with realistic delay
    const responseDelay = Math.random() * 2000 + 1500; // 1.5-3.5 seconds
    setTimeout(() => {
      setIsTyping(false);

      // Generate more contextual responses based on user input
      const userInput = newUserMessage.content.toLowerCase();
      let aiResponse = "";

      if (userInput.includes("product") || userInput.includes("description")) {
        aiResponse = "I can help you optimize your product descriptions! Here are some quick wins:\n\n🎯 **Use power words** like 'exclusive', 'limited', 'premium'\n📊 **Add specific details** - sizes, materials, dimensions\n💡 **Include use cases** - when and how to use the product\n⭐ **Highlight unique selling points**\n\nWant me to analyze a specific product for you?";
      } else if (userInput.includes("seo") || userInput.includes("search")) {
        aiResponse = "Great question about SEO! Here's how to boost your store's search visibility:\n\n🔍 **Keyword research** - Use tools like Google Keyword Planner\n📝 **Optimize product titles** - Include main keywords naturally\n🏷️ **Meta descriptions** - Write compelling 150-160 character summaries\n📸 **Alt text for images** - Describe your product images\n🔗 **Internal linking** - Connect related products\n\nShould I audit your current SEO setup?";
      } else if (userInput.includes("conversion") || userInput.includes("sales")) {
        aiResponse = "Let's boost your conversion rate! Here are proven strategies:\n\n💳 **Simplify checkout** - Reduce steps and form fields\n🛡️ **Add trust signals** - Security badges, reviews, guarantees\n⚡ **Improve page speed** - Optimize images and code\n📱 **Mobile optimization** - Ensure smooth mobile experience\n🎁 **Create urgency** - Limited time offers, stock counters\n\nWant me to analyze your current conversion funnel?";
      } else {
        aiResponse = `I understand you're asking about "${newUserMessage.content}". I'm here to help with all aspects of your Shopify store!\n\n🛍️ **Store optimization**\n📈 **Sales strategies** \n🎨 **Design improvements**\n📊 **Analytics insights**\n🔧 **Technical support**\n\nCould you tell me more specifically what you'd like to improve? I can provide more targeted advice!`;
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: aiResponse,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    }, responseDelay);
  };

  // Function to handle Enter key press in input field
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  // Function to handle file attachment (placeholder)
  const handleAttachment = () => {
    // TODO: Implement file attachment functionality
    console.log("File attachment clicked - to be implemented");
  };

  // Function to toggle sidebar collapse/expand
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Get mode display properties
  const modeDisplay = getModeDisplay(userMode);

  return (
    <>
      {/*
        Fixed positioning container that stays on the right side of the browser
        - Uses CSS classes for better styling and responsive behavior
        - position: fixed keeps it in place during scrolling
        - right: 0 positions it at the right edge
        - top: 0 and bottom: 0 make it full height
        - z-index: 1000 ensures it stays above other content
      */}
      <div
        className={`${styles.sidebarContainer} ${
          isCollapsed ? styles.collapsed : styles.expanded
        }`}
      >
        {/* Main sidebar card */}
        <Card>
          <div className={styles.sidebarContent}>
            {/* Header section with mode badge and collapse button */}
            <div className={styles.sidebarHeader}>
              <Box padding="300">
              <InlineStack align="space-between" blockAlign="center">
                {!isCollapsed && (
                  <InlineStack gap="200" blockAlign="center">
                    <Icon source={ChatIcon} tone="base" />
                    <Text as="h3" variant="headingSm">
                      AI Assistant
                    </Text>
                    <Badge tone={modeDisplay.tone}>{modeDisplay.text}</Badge>
                  </InlineStack>
                )}
                
                {/* Collapse/Expand toggle button */}
                <Button
                  variant="tertiary"
                  size="slim"
                  icon={isCollapsed ? ChevronLeftIcon : ChevronRightIcon}
                  onClick={toggleSidebar}
                  accessibilityLabel={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                />
              </InlineStack>
              </Box>
            </div>

            {/* Only show chat content when sidebar is expanded */}
            {!isCollapsed && (
              <>
                {/* Messages area - scrollable container */}
                <div className={styles.messagesContainer}>
                  <Scrollable style={{ height: "100%" }}>
                    <Box padding="300">
                      <BlockStack gap="300">
                        {messages.map((message) => (
                          <div key={message.id}>
                            {/* Message bubble with different styling for user vs assistant */}
                            <div
                              className={`${styles.messageBubble} ${
                                message.type === "user"
                                  ? styles.userMessage
                                  : styles.assistantMessage
                              }`}
                            >
                              <Box padding="200">
                              <BlockStack gap="100">
                                {/* Message sender label */}
                                <Text 
                                  as="span" 
                                  variant="bodySm" 
                                  tone={message.type === "user" ? "text-inverse" : "subdued"}
                                >
                                  {message.type === "user" ? "You" : "AI Assistant"}
                                </Text>
                                
                                {/* Message content */}
                                <div style={{ whiteSpace: "pre-wrap" }}>
                                  <Text
                                    as="p"
                                    variant="bodyMd"
                                    tone={message.type === "user" ? "text-inverse" : undefined}
                                  >
                                    {message.content}
                                  </Text>
                                </div>

                                {/* Timestamp */}
                                <Text
                                  as="span"
                                  variant="bodySm"
                                  tone={message.type === "user" ? "text-inverse" : "subdued"}
                                >
                                  {message.timestamp.toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </Text>
                              </BlockStack>
                              </Box>
                            </div>
                          </div>
                        ))}

                        {/* Typing indicator when AI is responding */}
                        {isTyping && (
                          <div>
                            <div className={`${styles.messageBubble} ${styles.assistantMessage}`}>
                              <Box padding="200">
                                <BlockStack gap="100">
                                  <Text as="span" variant="bodySm" tone="subdued">
                                    AI Assistant
                                  </Text>
                                  <div className={styles.typingIndicator}>
                                    <div className={styles.typingDot}></div>
                                    <div className={styles.typingDot}></div>
                                    <div className={styles.typingDot}></div>
                                    <div style={{ marginLeft: '8px' }}>
                                      <Text as="span" variant="bodySm" tone="subdued">
                                        Thinking...
                                      </Text>
                                    </div>
                                  </div>
                                </BlockStack>
                              </Box>
                            </div>
                          </div>
                        )}

                        {/* Invisible div to enable auto-scrolling to bottom */}
                        <div ref={messagesEndRef} />
                      </BlockStack>
                    </Box>
                  </Scrollable>
                </div>

                <Divider />

                {/* Input area for new messages */}
                <div className={styles.inputArea}>
                  <Box padding="300">
                  <BlockStack gap="200">
                    {/* Text input with attachment and send buttons */}
                    <div style={{ position: "relative" }} onKeyDown={handleKeyPress}>
                      <TextField
                        label=""
                        value={currentMessage}
                        onChange={setCurrentMessage}
                        placeholder="Ask me anything about your store..."
                        multiline={2}
                        autoComplete="off"
                        connectedLeft={
                          <Button
                            variant="tertiary"
                            icon={AttachmentIcon}
                            onClick={handleAttachment}
                            accessibilityLabel="Attach file"
                          />
                        }
                        connectedRight={
                          <Button
                            variant="primary"
                            icon={SendIcon}
                            onClick={handleSendMessage}
                            disabled={!currentMessage.trim() || isTyping}
                            accessibilityLabel="Send message"
                          />
                        }
                      />
                    </div>
                    
                    {/* Helper text based on user mode */}
                    <Text as="p" variant="bodySm" tone="subdued" alignment="center">
                      {userMode === "free" && "5 messages remaining today"}
                      {userMode === "pro" && "Unlimited messages • Preview & Apply changes"}
                      {userMode === "developer" && "Full access • Debug tools • API access"}
                    </Text>
                  </BlockStack>
                  </Box>
                </div>
              </>
            )}
          </div>
        </Card>
      </div>
    </>
  );
}
