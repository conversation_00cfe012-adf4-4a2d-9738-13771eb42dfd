# ChatbotSidebar Component

This document explains the structure and functionality of the ChatbotSidebar component - a fixed AI assistant that acts as a Shopify Copilot.

## Overview

The ChatbotSidebar is a **fixed positioning component** that stays visible on the right side of the browser window across all pages of the Shopify app. It provides merchants with constant access to AI assistance for store optimization and management.

## Key Features

### 🔒 **Always Visible & Persistent**
- **Fixed positioning** to the right side of the browser window
- **Stays visible** across all app pages (Home, Dashboard, Settings, etc.)
- **Persists through navigation** - never disappears when switching pages
- **Survives page reloads** within the Shopify admin

### 🎯 **Three User Modes**
- **🟢 Free**: Basic AI suggestions, 5 messages per day, basic store analysis
- **🟡 Pro**: Unlimited messages, preview & apply changes, advanced analytics  
- **🔵 Developer**: Everything in Pro + raw diffs, debug tools, API access

### 💬 **Complete Chat Interface**
- **Scrollable message history** with user and assistant messages
- **Text input field** with multi-line support
- **File attachment button** (📎) for uploading documents/images
- **Send button** (➤) with keyboard shortcut (Enter)
- **Collapsible sidebar** to save screen space

### 🎨 **Professional Design**
- **Polaris UI components** for consistent Shopify styling
- **Responsive design** that works on all screen sizes
- **Accessibility features** with proper ARIA labels
- **Smooth animations** and hover effects

## Technical Implementation

### Component Structure

```typescript
interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

type UserMode = "free" | "pro" | "developer";
```

### State Management

```typescript
const [isCollapsed, setIsCollapsed] = useState(false);
const [currentMessage, setCurrentMessage] = useState("");
const [userMode] = useState<UserMode>("pro");
const [messages, setMessages] = useState<ChatMessage[]>([...]);
```

### Fixed Positioning CSS

The sidebar uses **CSS fixed positioning** to stay in place:

```css
.sidebarContainer {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: width 0.3s ease-in-out;
}
```

**Why this works:**
- `position: fixed` removes the element from normal document flow
- `right: 0` positions it at the right edge of the viewport
- `top: 0` and `bottom: 0` make it full height
- `z-index: 1000` ensures it stays above other content
- The element stays in the same position even when scrolling or navigating

### Integration with App Layout

The sidebar is included in the main app layout (`app/routes/app.tsx`):

```jsx
export default function App() {
  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <NavMenu>...</NavMenu>
      <Outlet /> {/* Page content */}
      <ChatbotSidebar /> {/* Always visible sidebar */}
    </AppProvider>
  );
}
```

This ensures the sidebar appears on **every page** of the app.

## File Structure

```
app/
├── components/
│   ├── ChatbotSidebar.tsx           # Main component
│   └── ChatbotSidebar.module.css    # Styling
├── routes/
│   ├── app.tsx                      # Layout with sidebar integration
│   ├── app._index/                  # Homepage
│   ├── app.dashboard.tsx            # Dashboard page
│   └── ...other pages
docs/
└── README-ChatbotSidebar.md         # This documentation
```

## Usage Examples

### Basic Integration

```jsx
import { ChatbotSidebar } from "../components/ChatbotSidebar";

// In your app layout
<ChatbotSidebar />
```

### Customizing User Mode

```jsx
// In the component (currently hardcoded for demo)
const [userMode] = useState<UserMode>("pro");

// In a real app, this would come from:
// - User subscription data
// - Database settings
// - Shopify app billing API
```

## Mock Data & Testing

The component includes realistic mock data for testing:

### Sample Messages
- Welcome message from AI assistant
- User question about product descriptions
- AI response with actionable tips
- Proper timestamps and message types

### Simulated AI Responses
When you send a message, the component simulates an AI response after 1 second delay.

## Responsive Design

### Desktop (>768px)
- Full 400px width when expanded
- 60px width when collapsed
- Smooth transitions between states

### Mobile (≤768px)
- Full screen width when expanded
- 50px width when collapsed
- Touch-friendly interface

## Accessibility Features

### ARIA Labels
- Proper button labels for screen readers
- Descriptive text for all interactive elements

### Keyboard Navigation
- **Enter key** sends messages
- **Tab navigation** through all controls
- **Focus indicators** for better visibility

### High Contrast Support
- Respects user's contrast preferences
- Clear visual boundaries between elements

## Future Enhancements

### Backend Integration
- Connect to real AI service (OpenAI, Claude, etc.)
- Store conversation history in database
- User authentication and session management

### Advanced Features
- **Typing indicators** when AI is responding
- **Message reactions** (thumbs up/down)
- **Conversation export** functionality
- **Voice input/output** capabilities
- **File upload processing** for documents and images

### Analytics
- Track user engagement with the assistant
- Monitor most common questions
- Measure conversion from suggestions to actions

## Beginner Developer Notes

### How Fixed Positioning Works
Fixed positioning is like "gluing" an element to the browser window:
- It doesn't move when you scroll the page
- It doesn't move when you navigate to different pages
- It stays in the exact same spot relative to the browser window
- Other content flows around/behind it

### Why Use CSS Modules
CSS modules prevent style conflicts:
- Each component gets unique class names
- Styles are scoped to the component
- No global CSS pollution
- Better maintainability

### State Management Patterns
The component uses React hooks for state:
- `useState` for component-level state
- `useRef` for DOM references (auto-scrolling)
- `useEffect` for side effects (scroll to bottom)

This ChatbotSidebar provides a solid foundation for an AI-powered Shopify assistant that merchants can rely on for constant support and guidance.
