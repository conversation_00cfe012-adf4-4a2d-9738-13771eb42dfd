/* 
 * ChatbotSidebar Styles
 * Custom styles to enhance the Polaris components for the chatbot sidebar
 */

/* Fixed sidebar container - Enhanced with glassmorphism */
.sidebarContainer {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  box-shadow: -4px 0 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20px);
}

.sidebarContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: -1;
}

/* Collapsed state - Enhanced with smooth animation */
.sidebarContainer.collapsed {
  width: 60px;
  box-shadow: -2px 0 16px rgba(0, 0, 0, 0.08);
}

.sidebarContainer.collapsed .sidebarContent {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Expanded state - Enhanced with better shadow */
.sidebarContainer.expanded {
  width: 400px;
  box-shadow: -8px 0 40px rgba(0, 0, 0, 0.15);
}

/* Hover effect for collapsed state */
.sidebarContainer.collapsed:hover {
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.12);
  transform: translateX(-2px);
}

/* Main sidebar content - Enhanced with subtle gradient */
.sidebarContent {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.sidebarContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.05) 50%, transparent 100%);
}

/* Header section - Enhanced with gradient and branding */
.sidebarHeader {
  border-bottom: 1px solid var(--p-color-border);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  backdrop-filter: blur(10px);
  position: relative;
}

.sidebarHeader::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--p-color-border) 50%, transparent 100%);
}

/* App icon styling */
.appIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;
}

.appIcon:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

/* Custom mode badge container */
.modeBadgeContainer {
  margin-top: 4px;
}

.customModeBadge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.customModeBadge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modeIcon {
  font-size: 14px;
  line-height: 1;
}

/* Header actions styling */
.headerAction {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.headerAction:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.headerAction:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Search container styling */
.searchContainer {
  margin-top: 12px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Messages container */
.messagesContainer {
  flex: 1;
  overflow: hidden;
  background: var(--p-color-bg-surface);
  position: relative;
}

/* Empty state styling */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem 1rem;
  text-align: center;
}

.welcomeSection {
  margin-bottom: 2rem;
}

.welcomeIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

.quickStartGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  width: 100%;
  max-width: 300px;
  margin-bottom: 1.5rem;
}

.quickStartCard {
  background: white;
  border: 1px solid var(--p-color-border);
  border-radius: 12px;
  padding: 16px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.quickStartCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--p-color-border-strong);
}

.suggestionIcon {
  font-size: 1.5rem;
  margin-bottom: 8px;
}

.helpSection {
  max-width: 280px;
  padding: 12px;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

/* Bounce animation for welcome icon */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Message container for better spacing */
.messageContainer {
  margin-bottom: 16px;
}

/* Individual message bubble - Enhanced modern design */
.messageBubble {
  border-radius: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  max-width: 85%;
  word-wrap: break-word;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.messageBubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Add subtle glow effect for user messages */
.userMessage:hover {
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Add subtle elevation for assistant messages */
.assistantMessage:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Message avatars */
.messageAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.userAvatar {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.assistantAvatar {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid var(--p-color-border);
}

/* Message content styling */
.messageContent {
  line-height: 1.5;
  word-break: break-word;
}

.markdownContent {
  line-height: 1.6;
}

.markdownContent strong {
  font-weight: 600;
  color: inherit;
}

.markdownContent em {
  font-style: italic;
  color: inherit;
}

/* Status icon styling */
.statusIcon {
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.statusIcon:hover {
  opacity: 1;
}

/* Message actions */
.messageActions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.messageBubble:hover .messageActions {
  opacity: 1;
}

.actionButton {
  background: none;
  border: none;
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.actionButton:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

/* User message styling - Modern blue gradient */
.userMessage {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  margin-left: 40px;
  margin-right: 8px;
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
  border: none;
  position: relative;
}

.userMessage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: inherit;
  pointer-events: none;
}

/* Assistant message styling - Clean white with subtle shadow */
.assistantMessage {
  background: white;
  color: var(--p-color-text);
  margin-left: 8px;
  margin-right: 40px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--p-color-border-secondary);
  position: relative;
}

.assistantMessage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.01) 100%);
  border-radius: inherit;
  pointer-events: none;
}

/* Enhanced input area with better styling */
.inputArea {
  border-top: 1px solid var(--p-color-border);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  position: relative;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
}

.inputArea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--p-color-border) 50%, transparent 100%);
}

/* Input container with enhanced styling */
.inputContainer {
  position: relative;
}

.inputWrapper {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.inputWrapper.inputFocused {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  transform: translateY(-1px);
}

/* Attachment container and menu */
.attachmentContainer {
  position: relative;
}

.attachmentMenu {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: white;
  border: 1px solid var(--p-color-border);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-bottom: 8px;
  min-width: 120px;
  animation: slideUp 0.2s ease;
}

.attachmentOption {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.attachmentOption:hover {
  background: var(--p-color-bg-surface-hover);
}

.attachmentOption:first-child {
  border-radius: 8px 8px 0 0;
}

.attachmentOption:last-child {
  border-radius: 0 0 8px 8px;
}

/* Input status bar */
.inputStatus {
  margin-top: 8px;
  padding: 0 4px;
}

/* Quick actions */
.quickAction {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.quickAction:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

/* Quick replies styling */
.quickRepliesContainer {
  margin-bottom: 12px;
  padding: 0 4px;
}

.quickReplies {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 6px;
}

.quickReplyButton {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid var(--p-color-border);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--p-color-text);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quickReplyButton:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: var(--p-color-border-strong);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quickReplyButton:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Helper text styling */
.helperText {
  padding: 0 4px;
}

/* Slide up animation for attachment menu */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mode badge styling */
.modeBadge {
  font-weight: 600;
}

/* Enhanced responsive design for better mobile experience */
@media (max-width: 768px) {
  .sidebarContainer.expanded {
    width: 100vw;
    left: 0;
    right: 0;
    z-index: 1001;
  }

  .sidebarContainer.collapsed {
    width: 50px;
  }

  /* Adjust message bubbles for mobile */
  .userMessage {
    margin-left: 20px;
    margin-right: 4px;
    max-width: 90%;
  }

  .assistantMessage {
    margin-left: 4px;
    margin-right: 20px;
    max-width: 90%;
  }

  /* Adjust quick start grid for mobile */
  .quickStartGrid {
    grid-template-columns: 1fr;
    gap: 8px;
    max-width: 280px;
  }

  .quickStartCard {
    padding: 12px 8px;
  }

  /* Adjust input area for mobile */
  .inputWrapper {
    border-radius: 8px;
  }

  .attachmentMenu {
    min-width: 100px;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  .sidebarContainer.collapsed {
    width: 40px;
  }

  .welcomeIcon {
    font-size: 2.5rem;
  }

  .emptyState {
    min-height: 300px;
    padding: 1rem 0.5rem;
  }

  .quickStartCard {
    padding: 10px 6px;
  }

  .suggestionIcon {
    font-size: 1.2rem;
  }
}

/* Enhanced animation for message appearance */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes messageFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.messageBubble {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Special animation for user messages (slide from right) */
.userMessage {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: right center;
}

/* Special animation for assistant messages (slide from left) */
.assistantMessage {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left center;
}

/* Enhanced typing indicator with better animations */
.typingMessage {
  animation: fadeInUp 0.3s ease;
}

.enhancedTypingIndicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.typingAnimation {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typingDot {
  width: 10px;
  height: 10px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 50%;
  animation: typingBounce 1.4s infinite ease-in-out;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.4);
}

.typingDot:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDot:nth-child(2) {
  animation-delay: -0.16s;
}

.typingDot:nth-child(3) {
  animation-delay: 0s;
}

.typingText {
  animation: typingTextPulse 2s infinite ease-in-out;
}

/* Enhanced typing animations */
@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0.8) translateY(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2) translateY(-6px);
    opacity: 1;
  }
}

@keyframes typingTextPulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling for webkit browsers */
.messagesContainer ::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer ::-webkit-scrollbar-track {
  background: var(--p-color-bg-surface);
}

.messagesContainer ::-webkit-scrollbar-thumb {
  background: var(--p-color-border);
  border-radius: 3px;
}

.messagesContainer ::-webkit-scrollbar-thumb:hover {
  background: var(--p-color-border-strong);
}

/* Enhanced focus states for better accessibility */
.sidebarContainer button:focus,
.quickStartCard:focus,
.attachmentOption:focus,
.actionButton:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  border-radius: 4px;
}

.quickStartCard:focus {
  transform: translateY(-2px);
  box-shadow: 0 0 0 2px #2563eb, 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attachmentOption:focus {
  background: rgba(37, 99, 235, 0.1);
  outline: 2px solid #2563eb;
  outline-offset: 1px;
}

.actionButton:focus {
  background: rgba(37, 99, 235, 0.1);
  transform: scale(1.1);
}

/* Focus-visible for better keyboard navigation */
.sidebarContainer button:focus-visible,
.quickStartCard:focus-visible,
.attachmentOption:focus-visible,
.actionButton:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Skip to content link for screen readers */
.skipLink {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #2563eb;
  color: white;
  padding: 8px 12px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
  font-weight: 500;
}

.skipLink:focus {
  top: 6px;
}

/* Screen reader only content */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .messageBubble {
    border: 1px solid var(--p-color-border-strong);
  }
  
  .userMessage {
    border-color: var(--p-color-border-info);
  }
  
  .assistantMessage {
    border-color: var(--p-color-border);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sidebarContainer {
    transition: none;
  }
  
  .messageBubble {
    animation: none;
    transition: none;
  }
  
  .typingDot {
    animation: none;
  }
}
