/* 
 * ChatbotSidebar Styles
 * Custom styles to enhance the Polaris components for the chatbot sidebar
 */

/* Fixed sidebar container - Enhanced with glassmorphism */
.sidebarContainer {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  box-shadow: -4px 0 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20px);
}

.sidebarContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: -1;
}

/* Collapsed state - Enhanced with smooth animation */
.sidebarContainer.collapsed {
  width: 60px;
  box-shadow: -2px 0 16px rgba(0, 0, 0, 0.08);
}

.sidebarContainer.collapsed .sidebarContent {
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Expanded state - Enhanced with better shadow */
.sidebarContainer.expanded {
  width: 400px;
  box-shadow: -8px 0 40px rgba(0, 0, 0, 0.15);
}

/* Hover effect for collapsed state */
.sidebarContainer.collapsed:hover {
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.12);
  transform: translateX(-2px);
}

/* Main sidebar content - Enhanced with subtle gradient */
.sidebarContent {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.sidebarContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.05) 50%, transparent 100%);
}

/* Header section - Enhanced with gradient */
.sidebarHeader {
  border-bottom: 1px solid var(--p-color-border);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  backdrop-filter: blur(10px);
  position: relative;
}

.sidebarHeader::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--p-color-border) 50%, transparent 100%);
}

/* Messages container */
.messagesContainer {
  flex: 1;
  overflow: hidden;
  background: var(--p-color-bg-surface);
}

/* Individual message bubble - Enhanced modern design */
.messageBubble {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  max-width: 85%;
  word-wrap: break-word;
  backdrop-filter: blur(10px);
}

.messageBubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Add subtle glow effect for user messages */
.userMessage:hover {
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Add subtle elevation for assistant messages */
.assistantMessage:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* User message styling - Modern blue gradient */
.userMessage {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  margin-left: 40px;
  margin-right: 8px;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  border: none;
}

/* Assistant message styling - Clean white with subtle shadow */
.assistantMessage {
  background: white;
  color: var(--p-color-text);
  margin-left: 8px;
  margin-right: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--p-color-border-secondary);
}

/* Input area - Enhanced with gradient and shadow */
.inputArea {
  border-top: 1px solid var(--p-color-border);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  position: relative;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.inputArea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--p-color-border) 50%, transparent 100%);
}

/* Mode badge styling */
.modeBadge {
  font-weight: 600;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .sidebarContainer.expanded {
    width: 100vw;
    left: 0;
  }
  
  .sidebarContainer.collapsed {
    width: 50px;
  }
}

/* Enhanced animation for message appearance */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes messageFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.messageBubble {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Special animation for user messages (slide from right) */
.userMessage {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: right center;
}

/* Special animation for assistant messages (slide from left) */
.assistantMessage {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left center;
}

/* Enhanced typing indicator */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
}

.typingDot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 50%;
  animation: typingAnimation 1.6s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.typingDot:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDot:nth-child(2) {
  animation-delay: -0.16s;
}

.typingDot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.6) translateY(0);
    opacity: 0.4;
  }
  40% {
    transform: scale(1) translateY(-4px);
    opacity: 1;
  }
}

/* Pulse animation for the entire typing indicator */
@keyframes typingPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.typingIndicator {
  animation: typingPulse 2s infinite ease-in-out;
}

/* Scrollbar styling for webkit browsers */
.messagesContainer ::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer ::-webkit-scrollbar-track {
  background: var(--p-color-bg-surface);
}

.messagesContainer ::-webkit-scrollbar-thumb {
  background: var(--p-color-border);
  border-radius: 3px;
}

.messagesContainer ::-webkit-scrollbar-thumb:hover {
  background: var(--p-color-border-strong);
}

/* Focus states for accessibility */
.sidebarContainer button:focus {
  outline: 2px solid var(--p-color-border-focus);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .messageBubble {
    border: 1px solid var(--p-color-border-strong);
  }
  
  .userMessage {
    border-color: var(--p-color-border-info);
  }
  
  .assistantMessage {
    border-color: var(--p-color-border);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sidebarContainer {
    transition: none;
  }
  
  .messageBubble {
    animation: none;
    transition: none;
  }
  
  .typingDot {
    animation: none;
  }
}
