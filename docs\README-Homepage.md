# AI Assistant Homepage Component

This document explains the structure and functionality of the AI Assistant for Shopify homepage component (`app._index.tsx`).

## Overview

The homepage is built using **Shopify Polaris** components and follows Shopify's design system best practices. It serves as the main landing page for merchants when they open the AI Assistant app.

## Component Structure

### 1. **Hero Section**
- **Purpose**: Introduces the app and its main value proposition
- **Components Used**:
  - `Text` with `heading2xl` variant for the main title
  - YouTube iframe embed for the demo video
  - Feature highlights with icons
  - Primary `Button` with chat icon for the main CTA

**Key Features**:
- Responsive design that works on all screen sizes
- Live YouTube demo video (https://www.youtube.com/watch?v=xNUx-rMGvvw)
- Enhanced video section with feature highlights
- Fallback YouTube link for accessibility
- Clear call-to-action to start using the chat feature

### 2. **Plans Section**
- **Purpose**: Shows the three subscription tiers (Free, Pro, Developer)
- **Components Used**:
  - `Grid` for responsive layout
  - `Card` components for each plan
  - `Badge` to highlight the popular plan
  - `Icon` with checkmarks for feature lists

**Plan Details**:
- **Free**: Basic AI suggestions, 5 chats/day, basic analysis
- **Pro**: Unlimited chats, preview changes, apply changes, advanced analytics
- **Developer**: Everything in Pro + raw diffs, debug tools, API access, priority support

### 3. **Support Section**
- **Purpose**: Provides multiple ways for users to get help
- **Components Used**:
  - `Grid` for responsive layout
  - `Card` with secondary background for visual distinction
  - `Icon` components for visual hierarchy

**Support Options**:
- **Email Support**: Direct email contact with 24-hour response time
- **Live Chat**: Real-time support during business hours
- **Help Documentation**: Links to FAQs and tutorials

### 4. **Footer Section**
- **Purpose**: Final call-to-action and additional engagement
- **Components Used**:
  - `Card` with secondary background
  - Multiple `Button` components for different actions

## Technical Implementation

### State Management
```typescript
const [isChatOpen, setIsChatOpen] = useState(false);
```
- Tracks whether the chat sidebar is currently open
- Used to update button text dynamically

### Event Handlers
```typescript
const openChat = () => {
  setIsChatOpen(true);
  console.log("Opening chat sidebar...");
};

const handleSupportChat = () => {
  console.log("Opening support chat...");
};
```
- Placeholder functions for chat functionality
- Ready to be connected to actual chat implementation

### CSS Styling
- Uses CSS modules (`app._index.module.css`) for custom styling
- Includes hover effects, responsive design, and accessibility improvements
- Follows Polaris design tokens and spacing

## Responsive Design

The component uses Polaris's responsive grid system:
```typescript
<Grid.Cell columnSpan={{xs: 6, sm: 3, md: 2, lg: 4, xl: 4}}>
```

**Breakpoints**:
- `xs`: Extra small screens (mobile)
- `sm`: Small screens (tablet portrait)
- `md`: Medium screens (tablet landscape)
- `lg`: Large screens (desktop)
- `xl`: Extra large screens (wide desktop)

## Accessibility Features

1. **Semantic HTML**: Proper heading hierarchy (`h1`, `h2`, `h3`)
2. **Icon Labels**: All icons have proper context
3. **Focus Management**: Keyboard navigation support
4. **Screen Reader Support**: Descriptive text for all interactive elements
5. **Color Contrast**: Follows WCAG guidelines through Polaris tokens

## Integration Points

### Backend Integration
- `loader` function authenticates users with Shopify
- Ready for data fetching when needed

### Video Section Features
- **YouTube Integration**: Direct embed of demo video
- **Responsive Design**: 16:9 aspect ratio that scales perfectly
- **Feature Highlights**: Visual indicators of key capabilities
- **Accessibility**: Fallback link to YouTube for users with embed issues
- **Enhanced Styling**: Gradient backgrounds, shadows, and hover effects

### Future Enhancements
- Connect `openChat()` to actual chat sidebar component
- Add billing integration for plan upgrades
- Connect support chat to real support system
- Add video analytics tracking
- Create multiple demo videos for different features

## File Structure
```
app/routes/
├── app._index.tsx           # Main homepage component
├── app._index.module.css    # Custom styles
docs/
└── README-Homepage.md       # This documentation
```

## Usage for Beginners

### Adding New Content
1. **New Section**: Add a new `Layout.Section` within the main `BlockStack`
2. **New Plan**: Copy an existing plan card and modify the content
3. **New Support Option**: Add another `Grid.Cell` in the support section

### Modifying Styles
1. **Polaris Styles**: Use Polaris props like `padding`, `background`, `variant`
2. **Custom Styles**: Add new classes to `app._index.module.css`
3. **Responsive**: Use the `columnSpan` prop for different screen sizes

### Common Polaris Components Used
- `Page`: Main page wrapper
- `Layout`: Responsive layout system
- `Card`: Content containers
- `Button`: Interactive elements
- `Text`: Typography with semantic variants
- `Icon`: Visual indicators
- `Grid`: Responsive grid system
- `BlockStack`: Vertical spacing
- `InlineStack`: Horizontal alignment

## Next Steps

1. **Install Dependencies**: Run `npm install` to get all required packages
2. **Start Development**: Run `npm run dev` to start the development server
3. **View in Shopify**: Install the app in a development store to see it in action
4. **Customize Content**: Update text, images, and styling to match your brand
5. **Add Functionality**: Connect the placeholder functions to real features

This homepage provides a solid foundation for your AI Assistant app and follows Shopify's best practices for embedded apps.
